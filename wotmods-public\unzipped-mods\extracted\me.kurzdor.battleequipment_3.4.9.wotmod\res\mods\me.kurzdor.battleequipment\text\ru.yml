# scope: battleequipment

settings/modDisplayName: Отображение оборудования в бою
settings/mode/label: Режим отображения
settings/mode/tooltip: Выберите режим отображения панели в бою.\n\n- <b>Всегда</b> - панель будет всегда отображаться и её возможно спрятать в бою.\n- <b>По клавише</b> - панель будет отображаться только при нажатии клавиши.
settings/mode/always/option: Всегда
settings/mode/onKey/option: По клавише
settings/position/label: Позиция панели
settings/position/tooltip: Выберите стартовую позицию панели в бою.\n\n- <b>Слева</b> - с левой стороны от панели расходников в бою.\n- <b>Справа</b> - с правой стороны от панели расходников в бою.\n- <b>Свободная</b> - позволяет перемещать панель (drag-n-drop) по всему экрану в его границах.
settings/position/left/option: Слева
settings/position/right/option: Справа
settings/position/free/option: Свободная
settings/showBadge/label: Отображать значок текущего комплекта оборудования в бою
settings/showBadge/tooltip: При включении данной настройки в бою будет видно значок с цифрой текущего комплекта оборудования в бою при наличии соответствующей прокачки полевой модернизации.\n\nОн также поддерживает отображение всплывающей подскзаки при наведении.
settings/hotkey/label: Горячая клавиша: Отображение панели в бою
settings/hotkey/tooltip: Определите клавишу для переключения отображения панели в бою, когда режим отображения "По клавише" выбран.
settings/hotkey/attention: Рекомендовано использовать одну клавишу.
battle/tooltip/header: Отображение оборудования в бою
battle/minimizer/tooltip/minimize/body: Нажмите, чтобы скрыть панель в бою.
battle/minimizer/tooltip/maximize/body: Нажмите, чтобы показать панель в бою.
battle/indexBadge/tooltip/0/body: В данный момент используется <b>первый комплект</b> оборудования в бою.
battle/indexBadge/tooltip/1/body: В данный момент используется <b>второй комплект</b> оборудования в бою.
