## Notice about config

### `assist/mode`

Possible values:

- radio - adds new radio assist counter. You can disable original assist counter if you want to see only splitted value(s).
- track - adds new track assist counter. You can disable original assist counter if you want to see only splitted value(s).
- both - adds both new radio and track assist counters. You can disable original assist counter if you want to see only splitted value(s).
- replace - replace original WG counter to formatted value from `assist/format` option. Requires enabled original assist counter option in game settings

! Warning: <PERSON><PERSON> doesn't remove nor force original assist counter. You have to enable it yourself if you use any of these options!

### `assist/format`

Available macroses:

- {{totalAssist}} - sum of radio and track assists values
- {{radioAssist}} - value of radio assist
- {{trackAssist}} - value of track assist

