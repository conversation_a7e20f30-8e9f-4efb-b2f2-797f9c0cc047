# uncompyle6 version 3.9.2
# Python bytecode version base 2.7 (62211)
# Decompiled from: Python 3.12.6 (tags/v3.12.6:a4a2d2b, Sep  6 2024, 20:11:23) [MSC v.1940 64 bit (AMD64)]
# Embedded file name: scripts/client/gui/modsSettingsApi/templates.py
# Compiled at: 2024-09-07 18:22:15
from ._constants import COMPONENT_TYPE

def createBase(type, text, tooltip=None):
    """ Helper to create base component

        :param type: Component type, MUST be one of COMPONENT_TYPE
        :param text: Component text
        :param tooltip: Component tooltip, optional
        
        :return: Base component
        """
    base = {'type': type, 'text': text}
    if tooltip is not None:
        base['tooltip'] = tooltip
    return base


def createControl(type, text, varName, value, tooltip=None, button=None):
    """ Helper to create control component

        :param type: Component type, MUST be one of COMPONENT_TYPE
        :param text: Component text
        :param varName: Variable name bound to this component that will store component's value in onModSettingsChanged callback
        :param value: Component value
        :param tooltip: Component tooltip, optional
        :param button: Component button, optional
        
        :return: Control component
        """
    control = createBase(type, text, tooltip)
    control.update({'varName': varName, 'value': value})
    if button is not None:
        control['button'] = button
    return control


def generateOptions(entries):
    """ Options generator for supported controls with tooltip generation support 
        
        :param entries: Option entries
        :type list-like of str, dict or list-like, i.e. (('label', 'tooltip', ), ('label', ), 'label', { 'label': 'label5' }, { 'label': 'label6', 'tooltip': 'tooltip' })

        :return: Options dictionary for supported AS3 components (dropdown and radio button group)
        """
    options = []
    for entry in entries:
        label = ''
        tooltip = None
        if isinstance(entry, (list, tuple, set)):
            try:
                label, tooltip = entry
            except:
                label = entry[0]

        elif isinstance(entry, dict):
            label = entry.get('label', '')
            tooltip = entry.get('tooltip', None)
        else:
            label = entry
        option = {}
        option['label'] = label
        if tooltip is not None:
            option['tooltip'] = tooltip
        options.append(option)

    return options


def createOptionsControl(type, text, varName, options, value, tooltip=None, button=None):
    """ Helper to create control with options component

        :param type: Component type, MUST be one of COMPONENT_TYPE
        :param text: Component text
        :param varName: Variable name bound to this component that will store component's value in onModSettingsChanged callback
        :param options: List of string value for component options
        :type options: list or tuple of str
        :param value: Component value
        :param tooltip: Component tooltip, optional
        :param button: Component button, optional
        
        :return: Control component with options
        """
    control = createControl(type, text, varName, value, tooltip, button)
    control['options'] = generateOptions(options)
    return control


def createStepper(type, text, varName, value, min, max, interval, tooltip=None, button=None):
    """ Helper to create stepper component (Slider, NumericStepper and RangeSlider)

        :param type: Component type, MUST be one of COMPONENT_TYPE
        :param text: Component text
        :param varName: Variable name bound to this component that will store component's value in onModSettingsChanged callback
        :param value: Component value
        :param min: Minimum value of stepper component
        :type min: int
        :param max: Maximum value of stepper component
        :type max: int
        :param interval: Step interval value
        :type interval: int
        :param tooltip: Component tooltip, optional
        :param button: Component button, optional
        
        :return: Stepper component
        """
    stepper = createControl(type, text, varName, value, tooltip, button)
    stepper.update({'minimum': min, 'maximum': max, 'snapInterval': interval})
    return stepper


def createButton(width=None, height=None, text=None, offsetTop=None, offsetLeft=None, icon=None, iconOffsetTop=None, iconOffsetLeft=None):
    """ Helper to create button for component
        
        :param width: Button width, optional
        :param height: Button height, optional
        :param text: Button text content, optional
        :param offsetTop: Button top offset relatively to component, optional
        :param offsetLeft: Button left offset relatively to component, optional
        :param icon: Path to icon location, optional
        :param iconOffsetTop: Icon top offset relatively to button, optional
        :param iconOffsetLeft: Icon left offset relatively to button, optional

        :return: Component button
        """
    button = {}
    if width is not None:
        button['width'] = width
    if height is not None:
        button['height'] = height
    if text is not None:
        button['text'] = text
    if offsetTop is not None:
        button['offsetTop'] = offsetTop
    if offsetLeft is not None:
        button['offsetLeft'] = offsetLeft
    if icon is not None and text is None:
        button['iconSource'] = icon
    if iconOffsetTop is not None:
        button['iconOffsetTop'] = iconOffsetTop
    if iconOffsetLeft is not None:
        button['iconOffsetLeft'] = iconOffsetLeft
    return button


def createEmpty():
    """ Helper to create empty component
        
        :return: Empty component
        """
    return {'type': 'Empty'}


def createLabel(text, tooltip=None):
    """ Helper to create Label component

        :param text: Component text
        :param tooltip: Component tooltip, optional

        :return: Label component
        """
    return createBase(COMPONENT_TYPE.LABEL, text, tooltip)


def createCheckbox(text, varName, value, tooltip=None, button=None):
    """ Helper to create Checkbox component

        :param text: Component text
        :param varName: Variable name bound to this component that will store component's value in onModSettingsChanged callback
        :param value: Component value
        :type value: bool
        :param tooltip: Component tooltip, optional
        :param button: Component button, optional

        :return: Checkbox component
        """
    return createControl(COMPONENT_TYPE.CHECKBOX, text, varName, value, tooltip, button)


def createRadioButtonGroup(text, varName, options, value, tooltip=None, button=None):
    """ Helper to create RadioButtonGroup component

        :param text: Component text
        :param varName: Variable name bound to this component that will store component's value in onModSettingsChanged callback
        :param value: Component value, index of options
        :type value: int
        :param tooltip: Component tooltip, optional
        :param button: Component button, optional

        :return: RadioButtonGroup component
        """
    return createOptionsControl(COMPONENT_TYPE.RADIO_BUTTON_GROUP, text, varName, options, value, tooltip, button)


def createDropdown(text, varName, options, value, tooltip=None, button=None, width=None):
    """ Helper to create Dropdown component

        :param text: Component text
        :param varName: Variable name bound to this component that will store component's value in onModSettingsChanged callback
        :param options: List of string value for component options
        :type options: list or tuple of str
        :param value: Component value, index of options
        :type value: int
        :param tooltip: Component tooltip, optional
        :param button: Component button, optional
        :param width: Component width, optional

        :return: Dropdown component
        """
    control = createOptionsControl(COMPONENT_TYPE.DROPDOWN, text, varName, options, value, tooltip, button)
    if width is not None:
        control['width'] = width
    return control


def createSlider(text, varName, value, min, max, interval, format='{{value}}', tooltip=None, button=None, width=None):
    """ Helper to create Slider component

        :param text: Component text
        :param varName: Variable name bound to this component that will store component's value in onModSettingsChanged callback
        :param value: Component value
        :type value: int
        :param min: Minimum value of stepper component
        :type min: int
        :param max: Maximum value of stepper component
        :type max: int
        :param interval: Step interval value
        :type interval: int
        :param format: Component value format template, defaults to '{{value}}' optional
        :param tooltip: Component tooltip, optional
        :param button: Component button, optional
        :param width: Component width, optional

        :return: Slider component
        """
    stepper = createStepper(COMPONENT_TYPE.SLIDER, text, varName, value, min, max, interval, tooltip, button)
    stepper['format'] = format
    if width is not None:
        stepper['width'] = width
    return stepper


def createInput(text, varName, value, tooltip=None, button=None, width=None):
    """ Helper to create Input component

        :param text: Component text
        :param varName: Variable name bound to this component that will store component's value in onModSettingsChanged callback
        :param value: Component value
        :type value: str
        :param tooltip: Component tooltip, optional
        :param button: Component button, optional
        :param width: Component width, optional

        :return: Input component
        """
    control = createControl(COMPONENT_TYPE.TEXT_INPUT, text, varName, value, tooltip, button)
    if width is not None:
        control['width'] = width
    return control


def createNumericStepper(text, varName, value, min, max, interval, tooltip=None, button=None, manual=False):
    """ Helper to create NumericStepper component

        :param text: Component text
        :param varName: Variable name bound to this component that will store component's value in onModSettingsChanged callback
        :param value: Component value
        :type value: int
        :param min: Minimum value of stepper component
        :type min: int
        :param max: Maximum value of stepper component
        :type max: int
        :param interval: Step interval value
        :type interval: int
        :param tooltip: Component tooltip, optional
        :param button: Component button, optional
        :param manual: Defines if user can manually type value, optional

        :return: NumericStepper component
        """
    stepper = createStepper(COMPONENT_TYPE.NUMERIC_STEPPER, text, varName, value, min, max, interval, tooltip, button)
    stepper['canManualInput'] = manual
    return stepper


def createHotkey(text, varName, value, tooltip=None, button=None):
    """ Helper to create Hotkey component

        :param text: Component text
        :param varName: Variable name bound to this component that will store component's value in onModSettingsChanged callback
        :param value: Component value
        :type value: list of BigWorld keys
        :param tooltip: Component tooltip, optional
        :param button: Component button, optional

        :return: Hotkey component
        """
    return createControl(COMPONENT_TYPE.HOTKEY, text, varName, value, tooltip, button)


def createColorChoice(text, varName, value, tooltip=None, button=None):
    """ Helper to create Hotkey component

        :param text: Component text
        :param varName: Variable name bound to this component that will store component's value in onModSettingsChanged callback
        :param value: Component value
        :type value: str of hex color code with or without hash
        :param tooltip: Component tooltip, optional
        :param button: Component button, optional

        :return: Hotkey component
        """
    if value.startswith('#'):
        value = value.lstrip('#')
    return createControl(COMPONENT_TYPE.COLOR_CHOICE, text, varName, value, tooltip, button)


def createRangeSlider(text, varName, value, min, max, interval, step, minRange, labelStep, labelPostfix, tooltip=None, button=None):
    """ Helper to create RangeSlider component

        :param text: Component text
        :param varName: Variable name bound to this component that will store component's value in onModSettingsChanged callback
        :param value: Component value
        :type value: list[int, int]
        :param min: Minimum value of stepper component
        :type min: int
        :param max: Maximum value of stepper component
        :type max: int
        :param interval: Step interval value
        :type interval: int
        :param step: Step
        :param minRange: Minimal range of values
        :param minRange: int
        :param labelStep: Steps of component labels
        :param labelStep: int
        :param labelPostfix: Postfix of component labels
        :param labelPostfix: str
        :param tooltip: Component tooltip, optional
        :param button: Component button, optional

        :return: RangeSlider component
        """
    stepper = createStepper(COMPONENT_TYPE.RANGE_SLIDER, text, varName, value, min, max, interval, tooltip, button)
    stepper.update({'divisionStep': step, 
       'minRangeDistance': minRange, 
       'divisionLabelStep': labelStep, 
       'divisionLabelPostfix': labelPostfix})
    return stepper
