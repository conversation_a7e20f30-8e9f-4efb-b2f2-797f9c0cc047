# CHANGELOG

### 1.2.11

- Added Last Stand gamemode to ignorelist

### 1.2.10

- Added Fall Tanks (Neon Raid) gamemode to ignorelist

### 1.2.9

- Added Cosmic Event battles to ignorelist

### 1.2.8

- Minor update

### 1.2.7

- Added Grinch battles to ignorelist

### 1.2.6

- Fix debug log in flash

### 1.2.5

- Localization implementation update
- Cleanup

### 1.2.4

- Added shell naming ignore logic for specific gamemodes

### 1.2.3

- Added Event Battles gamemode to ignorelist

### 1.2.2

- Added Races gamemode to ignorelist

### 1.2.1

- Removed obsolete info logs on mod start

### 1.2.0

- Migrate localization system from JSON to YAML

### 1.1.1

- Fix handling quantity or cooldown label change (i.e. 10 -> 9)
- Refactored default counter labels processing logic
- Partial restructure of Flash code

### 1.1.0

- Added ModsSettings API support
- Added ability to configure font size
- Added ability to show shell type in shell slots
- Reworked slots processing logic

### 1.0.0

- Initial public release

