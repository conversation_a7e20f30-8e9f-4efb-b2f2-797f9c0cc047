# uncompyle6 version 3.9.2
# Python bytecode version base 2.7 (62211)
# Decompiled from: Python 3.12.6 (tags/v3.12.6:a4a2d2b, Sep  6 2024, 20:11:23) [MSC v.1940 64 bit (AMD64)]
# Embedded file name: python\gui\modsListApi\hooks.py
# Compiled at: 2025-08-31 18:25:36
from frameworks.wulf import WindowLayer
from gui.app_loader.settings import APP_NAME_SPACE
from gui.impl.lobby.page.lobby_footer import LobbyFooter
from gui.Scaleform.framework.managers.containers import POP_UP_CRITERIA
from gui.Scaleform.framework.managers.loaders import SFViewLoadParams
from gui.shared import events, EVENT_BUS_SCOPE, g_eventBus
from gui.shared.personality import ServicesLocator
from ._constants import MODS_LIST_BUTTON_POPOVER, MODS_LIST_BUTTON_VIEW
from .views.modsButton import ModsButtonView
from .utils import get_parent_window, override
from .controller import g_controller
from .events import g_eventsManager

def show_popover():
    """
    Loads the popover view when the button is clicked.
    """
    app = ServicesLocator.appLoader.getApp(APP_NAME_SPACE.SF_LOBBY)
    if not app:
        return
    app.loadView(SFViewLoadParams(MODS_LIST_BUTTON_POPOVER, parent=get_parent_window()))


g_eventsManager.showPopover += show_popover

def show_button():
    """
    Loads the button view when the application is initialized.
    """
    app = ServicesLocator.appLoader.getApp(APP_NAME_SPACE.SF_LOBBY)
    if not app:
        return
    app.loadView(SFViewLoadParams(MODS_LIST_BUTTON_VIEW, parent=get_parent_window()))


def on_app_initialized(event):
    """
    Handles the application initialization event.
    """
    if event.ns != APP_NAME_SPACE.SF_LOBBY:
        return
    show_button()


g_eventBus.addListener(events.AppLifeCycleEvent.INITIALIZED, on_app_initialized, scope=EVENT_BUS_SCOPE.GLOBAL)

def onListUpdated():
    """
    Handles the list updated event.
    """
    app = ServicesLocator.appLoader.getApp(APP_NAME_SPACE.SF_LOBBY)
    if not app:
        return
    if not app.containerManager:
        return
    container = app.containerManager.getContainer(WindowLayer.WINDOW)
    if not container:
        return
    view = container.getView(criteria={(POP_UP_CRITERIA.VIEW_ALIAS): MODS_LIST_BUTTON_VIEW})
    if g_controller.isModsExist and not view:
        return show_button()


g_eventsManager.onListUpdated += onListUpdated

@override(LobbyFooter, '_initChildren')
def hooked_initChildren(baseMethod, baseObject):
    """
    Injects the button view into the lobby footer.
    """
    baseMethod(baseObject)
    baseObject.setChildView(ModsButtonView.buttonLayoutID(), ModsButtonView())
