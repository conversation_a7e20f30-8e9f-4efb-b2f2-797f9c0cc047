# scope: advancedpersonalefficiency

settings/modDisplayName: Advanced Personal Efficiency
settings/assist/enabled/label: Enable modified assist counter
settings/assist/enabled/tooltip: Enables ability to extend assist counter using "Assist counter mode".
settings/assist/mode/label: Assist counter mode
settings/assist/mode/tooltip: Select behaviour of assist counter.\n\n* Radio - adds extra radio assist counter\n* Track - adds extra track assist counter\n* Both - adds extra radio and track assist counters\n* Replace - replace original assist counter value to formatted assist value from "Assist counter format"
settings/assist/mode/radio/option: Radio
settings/assist/mode/track/option: Track
settings/assist/mode/both/option: Both
settings/assist/mode/replace/option: Replace
settings/assist/format/label: Original assist counter format
settings/assist/format/tooltip: Customize original assist counter's value using macroses.\n\nAvailable macroses: \n* {{totalAssist}} - sum of radio and track assists values\n* {{radioAssist}} - value of radio assist\n* {{trackAssist}} - value of track assist\n\nDefault value: {{totalAssist}} ({{radioAssist}} / {{trackAssist}})
settings/spotted/enabled/label: Enable spotted vehicles counter
settings/spotted/enabled/tooltip: Adds extra spotted vehicles counter to damage log panel.
settings/crits/enabled/label: Enable crits counter
settings/crits/enabled/tooltip: Adds extra devices crits counter to damage log panel.
