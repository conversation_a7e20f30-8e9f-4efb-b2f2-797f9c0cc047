# scope: battleequipment

settings/modDisplayName: Gefechtsausrüstung
settings/mode/label: Modus
settings/mode/tooltip: Wähle den Modus für das Gefecht.\n\n- <b>Immer</b> - Anzeige ist immer sichtbar und minimierbar im Gefecht.\n- <b>Auf Tastendruck</b> - Anzeige wird nur bei Tastendruck sichtbar.
settings/mode/always/option: Immer
settings/mode/onKey/option: Auf Tastendruck
settings/position/label: Anzeigeposition
settings/position/tooltip: Wähle die Startposition der Anzeige im Gefecht.\n\n- <b>Links</b> - auf der linken Seite der Verbrauchsmaterial-Anzeige im Gefecht.\n- <b>Rechts</b> - auf der rechten Seite der Verbrauchsmaterial-Anzeige im Gefecht.\n- <b>Frei</b> - ermöglicht das beliebige positionieren der Anzeige, innerhalb möglicher Grenzen.
settings/position/left/option: Links
settings/position/right/option: Rechts
settings/position/free/option: Frei
settings/showBadge/label: Nummer des Setups anzeigen
settings/showBadge/tooltip: Wenn diese Option aktiviert ist, wird die aktuelle Nummer des Setups im Gefecht angezeigt, wenn entsprechende Feldmodifikationen erforscht worden. Außerdem wird die Anzeige von Tooltips beim Hover unterstützt.
settings/hotkey/label: Taste: Sichtbarkeit im Gefecht umschalten
settings/hotkey/tooltip: Definiere einen Taste, um die Sichtbarkeit der Anzeige im Kampf umzuschalten, wenn der Modus "Auf Tastendruck" verwendet wird.
settings/hotkey/attention: Es wird empfohlen eine einzelne Taste zu verwenden.
battle/tooltip/header: Gefechtsausrüstung
battle/minimizer/tooltip/minimize/body: Klicke um die Anzeige zu minimieren.
battle/minimizer/tooltip/maximize/body: Klicke um die Anzeige zu maximieren
battle/indexBadge/tooltip/0/body: Derzeitig wird das <b>erste</b> Setup für die Ausrüstung verwendet.
battle/indexBadge/tooltip/1/body: Derzeitig wird das <b>zweite</b> Setup für die Ausrüstung verwendet.
