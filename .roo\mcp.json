{"mcpServers": {"sequentialthinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}}, "web-search": {"args": ["-y", "@modelcontextprotocol/open-websearch"], "command": "npx", "env": {"MODE": "stdio", "DEFAULT_SEARCH_ENGINE": "duckduck<PERSON>", "ALLOWED_SEARCH_ENGINES": "duckduckgo,bing,exa"}}}