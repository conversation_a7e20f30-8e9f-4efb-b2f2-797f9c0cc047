# uncompyle6 version 3.9.2
# Python bytecode version base 2.7 (62211)
# Decompiled from: Python 3.12.6 (tags/v3.12.6:a4a2d2b, Sep  6 2024, 20:11:23) [MSC v.1940 64 bit (AMD64)]
# Embedded file name: python\openwg_gameface.py
# Compiled at: 2025-08-29 21:56:35
import collections, json, logging, os, zipfile, BigWorld, Event, ResMgr
from gui.impl.gen_utils import DynAccessor, INVALID_RES_ID
from frameworks.wulf import ViewModel
from frameworks.wulf.view.array import Array, fillStringsArray
__all__ = (
 'on_ready', 'res_id_by_key', 'res_ids_by_mask', 'gf_mod_inject',
 'ModDynAccessor', 'ModInjectModel')
logger = logging.getLogger('RES_MAP')
logger.setLevel(logging.INFO)
RES_MAP_FILE_PATH = 'gui/unbound/res_map.json'
MOD_CONFIGS_PATH = 'mods/configs/res_map'
RESTART_FLAG_FILE = 'res_map_restart'

class ResMapManager():
    """Manager for building, validating, and applying custom resource map."""

    def __init__(self):
        """Initializes the ResMapManager.

        Sets up the validation event, locates config files from file system and game VFS,
        builds and validates the resource map, and triggers client restart if needed.
        """
        self.onResMapValidated = Event.SafeEvent()
        self.isResMapValidated = False
        self.items_mapping = {}
        res_mods_dir = None
        for path in ResMgr.openSection('../paths.xml')['Paths'].values():
            if os.path.isdir(path.asString):
                mod_folder_path = path.asString.replace('./', '')
                if not os.path.isdir(mod_folder_path):
                    continue
                res_mods_dir = mod_folder_path
                break

        res_map_file_path = os.path.normpath(os.path.join(res_mods_dir, RES_MAP_FILE_PATH))
        configs_fs = list(self.config_files_in_real_fs())
        configs_vfs = list(self.config_files_in_game_vfs(configs_fs))
        if not configs_fs and not configs_vfs:
            if os.path.isfile(res_map_file_path):
                os.remove(res_map_file_path)
                self.restart_client()
            return
        try:
            resource_map = self._build_resource_map(configs_fs, configs_vfs)
            if not resource_map:
                logger.error('Cannot build modded res_map')
                return
        except Exception:
            logger.exception('Failed to build res_map.json')

        try:
            client_restarting = self._validate_resource_map(resource_map, res_map_file_path)
            if client_restarting:
                logger.info('Stored new res_map.json, restarting client now')
                return
        except Exception:
            logger.exception('Failed to validate res_map.json')

        self.isResMapValidated = True
        self.onResMapValidated()
        if os.path.isfile(RESTART_FLAG_FILE):
            os.remove(RESTART_FLAG_FILE)
        return

    def _build_resource_map(self, configs_fs, configs_vfs):
        """Builds the resource map by merging original resource map items and mod items.

        Args:
            configs_fs (list): Config files found in the real filesystem.
            configs_vfs (list): Config files found in the game VFS.

        Returns:
            dict or None: Final merged resource map dictionary or None on failure.
        """
        packages = ['res/packages/%s' % x for x in os.listdir('res/packages') if x.startswith('gui-part')]
        resource_map_data = None
        for package in packages:
            resource_map_data = self.read_file_from_package(package, RES_MAP_FILE_PATH)
            if resource_map_data:
                break

        if not resource_map_data:
            logger.error('Cannot find res_map.json in game GUI packages %s', packages)
            return
        else:
            try:
                resource_map_data = resource_map_data.replace(',}', '}')
                resource_map = json.loads(resource_map_data)
            except ValueError:
                logger.exception('Failed to deserialize res_map.json')
                return

            for file_path in configs_fs:
                try:
                    with open(file_path, 'r') as file_handle:
                        mod_items = json.load(file_handle)
                        if mod_items:
                            self._add_mod_items_to_resource_map(mod_items, resource_map)
                except Exception:
                    logger.exception('Failed to read mod config file from FS (%s)', file_path)
                    continue

            for file_path in configs_vfs:
                try:
                    vfs_file = ResMgr.openSection(file_path)
                    if vfs_file is not None and ResMgr.isFile(file_path):
                        mod_items = json.loads(vfs_file.asBinary)
                        if mod_items:
                            self._add_mod_items_to_resource_map(mod_items, resource_map)
                except Exception:
                    logger.exception('Failed to read mod config file from VFS (%s)', file_path)
                    continue

            return resource_map

    def _add_mod_items_to_resource_map(self, mod_items, resource_map):
        """Adds mod items to the resource map.

        Args:
            mod_items (list): List of dictionary items with metadata and itemID.
            resource_map (dict): Existing resource map to be modified.
        """
        for item in mod_items:
            if 'itemID' not in item:
                logger.error('Item is missing mandatory key [itemID]')
                continue
            item_id = item['itemID']
            del item['itemID']
            if item_id in self.items_mapping:
                logger.error('Item with key %s already exists, skipping', item_id)
                continue
            numeric_item_id = len(resource_map)
            self.items_mapping[item_id] = numeric_item_id
            hex_item_id = hex(numeric_item_id)[2:]
            resource_map[hex_item_id] = item

    def _validate_resource_map(self, resource_map, res_map_file_path):
        """Validates and saves the resource map to res_mods folder.

        Args:
            resource_map (dict): The combined resource map.
            res_map_file_path (str): Path where the resource map should be saved.

        Returns:
            bool: True if client needs to be restarted, otherwise False.
        """
        sorted_data = sorted(resource_map.items(), key=(lambda x: int(x[0], 16)))
        ordered_dict = collections.OrderedDict(sorted_data)
        latest_file_data = json.dumps(ordered_dict, ensure_ascii=False, separators=(',',
                                                                                    ':'))
        res_map_folder = os.path.dirname(res_map_file_path)
        if not os.path.isdir(res_map_folder):
            os.makedirs(res_map_folder)
        old_file_data = None
        file_exists = os.path.isfile(res_map_file_path)
        if file_exists:
            with open(res_map_file_path, 'r') as file_handle:
                old_file_data = file_handle.read()
        if not file_exists or old_file_data != latest_file_data:
            with open(res_map_file_path, 'w') as file_handle:
                file_handle.write(latest_file_data)
            return self.restart_client()
        else:
            return

    def restart_client(self):
        """Triggers client restart if it has not been restarted before by as.

        Returns:
            bool: True if restart was triggered, False otherwise.
        """
        try:
            if os.path.isfile(RESTART_FLAG_FILE):
                return False
            with open(RESTART_FLAG_FILE, 'w') as file_handle:
                file_handle.write('')
        except Exception:
            logger.error('Cant write RESTART_FLAG_FILE')

        BigWorld.restartGame()
        return True

    @staticmethod
    def read_file_from_package(package_path, file_path):
        """Reads a file from a game package.

        Args:
            package_path (str): Path to the .pkg file.
            file_path (str): Relative file path inside the package.

        Returns:
            str or None: File contents if found, else None.
        """
        if not os.path.isfile(package_path):
            return
        with zipfile.ZipFile(package_path) as zip_file:
            file_path_lower = file_path.lower()
            for zip_info in zip_file.filelist:
                if file_path_lower == zip_info.filename.lower():
                    return zip_file.read(zip_info.filename)

    @staticmethod
    def config_files_in_real_fs():
        """Yields mod config files from the real file system.

        Yields:
            str: Full path to each JSON config file found.
        """
        if os.path.isdir(MOD_CONFIGS_PATH):
            for file_name in os.listdir(MOD_CONFIGS_PATH):
                if file_name.endswith('.json'):
                    file_path = os.path.normpath(os.path.join(MOD_CONFIGS_PATH, file_name))
                    if os.path.isfile(file_path):
                        yield file_path

    @staticmethod
    def config_files_in_game_vfs(config_files=[]):
        """Yields mod config files from the game VFS (virtual filesystem).

        Args:
            config_files (list, optional): Already-known config file names.

        Yields:
            str: VFS path to each new config file found.
        """
        config_files = list(x.lower() for x in config_files)
        vfs_dir = ResMgr.openSection(MOD_CONFIGS_PATH)
        if vfs_dir is not None and ResMgr.isDir(MOD_CONFIGS_PATH):
            for vfs_item in vfs_dir.keys():
                file_path = os.path.normpath(os.path.join(MOD_CONFIGS_PATH, vfs_item.lower()))
                if file_path not in config_files:
                    if ResMgr.isFile(file_path):
                        config_files.append(file_path)
                        yield file_path

        return


manager = ResMapManager()

def res_ids_by_mask(mask):
    """Finds all modIDs that contain the given substring.

    Args:
        mask (str): Substring to match inside item IDs.

    Yields:
        tuple[str, int]: (modID, UiResourceManager itemID)
    """
    for key in manager.items_mapping:
        if mask in key:
            yield (
             key, manager.items_mapping[key])


def res_id_by_key(key):
    """Retrieves the UiResourceManager itemID for a specific modID.

    Args:
        key (str): The modID identifier.

    Returns:
        int or INVALID_RES_ID: Corresponding UiResourceManager itemID or INVALID_RES_ID if not found.
    """
    return manager.items_mapping.get(key, INVALID_RES_ID)


def on_ready(callback):
    """Executes a callback when the res_map is validated.

    Args:
        callback (Callable): Function to call once the res_map is validated.
    """
    if manager.isResMapValidated:
        return callback()
    manager.onResMapValidated += callback


class ModDynAccessor(DynAccessor):
    """Custom dynamic accessor for accessing mod UiResourceManager itemID.

    Extends DynAccessor to defer setting resId until the res_map is validated.
    """
    __slots__ = ('__modID', )

    def __init__(self, modID='', resID=INVALID_RES_ID):
        """Initializes the ModDynAccessor with a given modID and optional resID.

        Args:
            modID (str): Identifier of the mod-defined UI resource item.
            resID (int): Initial resource ID (default is INVALID_RES_ID).
        """
        super(ModDynAccessor, self).__init__(resID)
        self.__modID = modID
        on_ready(self._setResID)

    def _setResID(self):
        """Sets the internal resId once the res_map has been validated.

        This method retrieves the correct UiResourceManager itemID for the stored modID
        and assigns it to the base DynAccessor's internal resId field.
        """
        self._DynAccessor__resId = res_id_by_key(self.__modID)


class ModInjectModel(ViewModel):
    """ViewModel that provides resources for the JS-side injector."""

    def __init__(self, name, styles=None, scripts=None, modules=None):
        """Initializes the ModInjectModel.

        Args:
            name (str): A unique name of your model used to link it from JS
            styles (list, optional): A list of style URLs for injection.
            scripts (list, optional): A list of script URLs for injection.
            modules (list, optional): A list of script URLs acts as modules for injection.
        """
        self._name = name
        self._styles = Array()
        fillStringsArray(styles or [], self._styles)
        self._scripts = Array()
        fillStringsArray(scripts or [], self._scripts)
        self._modules = Array()
        fillStringsArray(modules or [], self._modules)
        super(ModInjectModel, self).__init__(properties=3, commands=0)

    def _initialize(self):
        """Initializes ViewModel properties."""
        super(ModInjectModel, self)._initialize()
        self._addStringProperty('name', self._name)
        self._addArrayProperty('styles', self._styles)
        self._addArrayProperty('scripts', self._scripts)
        self._addArrayProperty('modules', self._modules)


def gf_mod_inject(model, name, styles=None, scripts=None, modules=None):
    """Adds the inject model as a property to another ViewModel.

    This makes the resource lists available to the JS-side injector.

    Args:
        model (ViewModel): The target ViewModel to which the injector model will be added.
        name (str): A unique name of your model used to link it from JS
        styles (list, optional): A list of style URLs.
        scripts (list, optional): A list of script URLs.
        modules (list, optional): A list of script URLs acts as modules for injection.
    """
    model._addViewModelProperty('ModInjectModel', ModInjectModel(name, styles, scripts, modules))
