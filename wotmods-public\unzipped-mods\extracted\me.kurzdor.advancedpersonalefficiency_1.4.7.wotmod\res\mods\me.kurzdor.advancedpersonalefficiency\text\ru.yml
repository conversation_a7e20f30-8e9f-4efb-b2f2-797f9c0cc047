# scope: advancedpersonalefficiency

settings/modDisplayName: Расширенная личная эффективность
settings/assist/enabled/label: Включить модифицированный счётчик ассиста
settings/assist/enabled/tooltip: Добавляет возможность расширить счётчик ассиста используя настройку "Режим счётчика ассиста".
settings/assist/mode/label: Режим счётчика ассиста
settings/assist/mode/tooltip: Выберите поведение счётчика ассиста.\n\n* Отображать счётчик ассиста по засвету - добавляет дополнительный счётчик ассиста по засвету\n* Отображать счётчик ассиста по катку - добавляет дополнительный счётчик ассиста по катку\n* Отображать оба типа счётчика ассиста - добавляет дополнительные счётчик ассиста по засвету и катку\n* Заменять значение оригинального счётчика - заменяет значение оригинального счетчика ассиста на форматированное значение ассиста с настройки "Шаблон значения оригинального счётчика ассиста"
settings/assist/mode/radio/option: Отображать счётчик ассиста по засвету
settings/assist/mode/track/option: Отображать счётчик ассиста по катку
settings/assist/mode/both/option: Отображать оба типа счётчика ассиста
settings/assist/mode/replace/option: Заменять значение оригинального счётчика
settings/assist/format/label: Шаблон значения оригинального счётчика ассиста
settings/assist/format/tooltip: Настройка значения оригинального счётчика используя макросы.\n\nДоступные макросы: \n* {{totalAssist}} - сумма значений ассиста по засвету и катку\n* {{radioAssist}} - текущее значение ассиста по засвету\n* {{trackAssist}} - текущее значение ассиста по катку\n\nЗначение по умолчанию: {{totalAssist}} ({{radioAssist}} / {{trackAssist}})
settings/spotted/enabled/label: Включить счётчик обнаруженых танков
settings/spotted/enabled/tooltip: Добавляет дополнительный счётчик обнаруженых танков до панели лога урона.
settings/crits/enabled/label: Включить счётчик критов
settings/crits/enabled/tooltip: Добавляет дополнительный счётчик критов до панели лога урона.
