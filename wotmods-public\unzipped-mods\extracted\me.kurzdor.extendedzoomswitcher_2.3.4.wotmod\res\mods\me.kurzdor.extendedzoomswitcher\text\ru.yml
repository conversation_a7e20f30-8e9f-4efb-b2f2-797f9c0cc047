# scope: extendedzoomswitcher

settings/modDisplayName: Быстрое переключение расширеного зума
settings/showMessage/label: Показывать уведомление при переключении
settings/showMessage/tooltip: При включении настройки мод будет уведомлять в бою о переключении настройки.
settings/restoreAfterBattle/label: Востанавливать предбоевое значение настройки
settings/restoreAfterBattle/tooltip: После окончания боя настройка зума будет возвращатся на добоевое значение если переключалась в бою.
settings/hotkeys/toggle/label: Горячая клавиша: Переключение настройки
settings/hotkeys/toggle/tooltip: Выберите горячую клавишу при нажатии на которую будет переключатся настройка расширенного зума.
battle/messages/label: Расширенный зум
battle/statuses/on/label: включён
battle/statuses/off/label: отключен
