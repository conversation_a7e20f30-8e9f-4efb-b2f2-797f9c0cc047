# uncompyle6 version 3.9.2
# Python bytecode version base 2.7 (62211)
# Decompiled from: Python 3.12.6 (tags/v3.12.6:a4a2d2b, Sep  6 2024, 20:11:23) [MSC v.1940 64 bit (AMD64)]
# Embedded file name: python\gui\modsListApi\__init__.py
# Compiled at: 2025-08-31 22:02:13
__version__ = '1.7.0'
try:
    import openwg_gameface
except ImportError:
    import logging
    logger = logging.getLogger()
    logger.error('\n' + '!!!   !!!   !!!   !!!   !!!   !!!   !!!   !!!   !!!   !!!   !!!   !!!   !!!   !!!   !!!\n!!!\n!!!   ModsListAPI requires the openwg_gameface module to function.\n!!!   Without it, this and other GF UI mods will not work correctly.\n!!!   Please download and install it from: https://gitlab.com/openwg/wot.gameface/-/releases/\n!!!\n!!!   !!!   !!!   !!!   !!!   !!!   !!!   !!!   !!!   !!!   !!!   !!!   !!!   !!!   !!!\n')
    import sys
    sys.exit()

from .controller import g_controller
from .....hooks import *
from .....views import *

class ModsListApiRepresentation(object):
    """
    ModsListApiRepresentation provides a static interface to the mods list controller.

    This class defines the public API for adding, updating, removing, and alerting on modifications.
    """

    @staticmethod
    def addModification(*args, **kwargs):
        """
        Adds a new modification to the list.

        :param id: Unique modification ID - required
        :param name: Modification name - required
        :param description: Modification hint (mouse over) - required
        :param icon: Modification icon (path from res_mods/<game_version>/) - required
        :param enabled: Is modification enabled (can be clicked) - required
        :param login: Show modification on Login Window - required
        :param lobby: Show modification in Lobby - required
        :param callback: Called on modification click - required
        """
        g_controller.addModification(*args, **kwargs)

    @staticmethod
    def updateModification(*args, **kwargs):
        """
        Updates an existing modification.

        :param id: Unique modification ID - required
        :param name: Modification name - optional
        :param description: Modification hint (mouse over) - optional
        :param icon: Modification icon (path from res_mods/<game_version>/) - optional
        :param enabled: Is modification enabled (can be clicked) - optional
        :param login: Show modification on Login Window - optional
        :param lobby: Show modification in Lobby - optional
        :param callback: Called on modification click - optional
        """
        g_controller.updateModification(*args, **kwargs)

    @staticmethod
    def removeModification(*args, **kwargs):
        """
        Removes a modification from the list.

        :param id: Unique modification ID - required
        """
        g_controller.removeModification(*args, **kwargs)

    @staticmethod
    def alertModification(*args, **kwargs):
        """
        Highlights a modification in the list.

        :param id: Unique modification ID - required
        """
        g_controller.alertModification(*args, **kwargs)

    @staticmethod
    def clearModificationAlert(*args, **kwargs):
        """
        Clears the alert state for a modification.

        :param id: Unique modification ID - required
        """
        g_controller.clearModificationAlert(*args, **kwargs)


g_modsListApi = ModsListApiRepresentation()
