# CHANGELOG

### 1.2.3

- Minor update

### 1.2.2

- Localization implementation update

### 1.2.1

- Fixed logging

### 1.2.0

- Added more badges to display in badges page in sorted order
- Added ability to add custom badge
- Added mod's custom badge
- Added badges mapping per user's databaseID
- Added support for overriding badge in invite popup
- Now disabled `alwaysReplaceBattleResults` option logic works properly

### 1.1.0

- Added ability to persist opened battle results badges (and now showing the current always) under `alwaysReplaceBattleResults` option
- Implmented badge replacement in Squads, Training Rooms and Strongholds

### 1.0.0

- Initial release

