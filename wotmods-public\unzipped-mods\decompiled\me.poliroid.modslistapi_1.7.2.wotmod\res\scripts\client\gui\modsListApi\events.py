# uncompyle6 version 3.9.2
# Python bytecode version base 2.7 (62211)
# Decompiled from: Python 3.12.6 (tags/v3.12.6:a4a2d2b, Sep  6 2024, 20:11:23) [MSC v.1940 64 bit (AMD64)]
# Embedded file name: python\gui\modsListApi\events.py
# Compiled at: 2025-08-31 18:26:15
import Event

class EventsManager(object):
    """
    Manages custom events for the ModsList API.
    """

    def __init__(self):
        """
        Initializes the event manager.
        """
        self.onListUpdated = Event.Event()
        self.invokeModification = Event.Event()
        self.showPopover = Event.Event()


g_eventsManager = EventsManager()
