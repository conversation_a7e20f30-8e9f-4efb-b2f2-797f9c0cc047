# scope: serialnumberchanger

settings/modDisplayName: Serial Number Changer
settings/replaceMode/label: Replace mode
settings/replaceMode/tooltip: Choose mode how to replace original serial number.\n\n<b>"Use the predefined value"</b> - show predefined value in <b>"New serial number value for player vehicle"</b> option.\n<b>"Use the random value"</b> - use random value that was generated by modification.\n<b>"Use the damage in battle"</b> - use current damage in battle.\n<b>"Use the total assist in battle"</b> - use current assist in battle (both track and spot assist).\n<b>"Use the total damage in battle"</b> - use summ of current damage and assist.\n<b>"Use the total damage required for MoE in battle"</b> - use total damage (damage and max assist) that counts to MoE.\n<b>"Use the battles count on this vehicle"</b> - use battles count from random stats on this vehicle.\n<b>"Use the average damage on this vehicle"</b> - use average damage from random stats on this vehicle.\n<b>"Use the frags count on this vehicle only in battle"</b> - use frags count on this vehicle in battle.\n<b>"Use the lifetime frags count on this vehicle"</b> - use frags count from lifetime random stats on this vehicle.\n<b>"Use the MoE value on this vehicle"</b> - use MoE value before battle in hangar and battle.
settings/replaceMode/note: In some situations it may fallback to <b>"New serial number value for player vehicle"</b> option.
settings/replaceMode/predefined/option: Use the predefined value
settings/replaceMode/random/option: Use the random value
settings/replaceMode/damage/option: Use the damage in battle
settings/replaceMode/totalAssist/option: Use the total assist in battle
settings/replaceMode/totalDamage/option: Use the total damage in battle
settings/replaceMode/totalMarkDamage/option: Use the total damage required for MoE in battle
settings/replaceMode/battlesCount/option: Use the battles count on this vehicle
settings/replaceMode/averageDamage/option: Use the average damage on this vehicle
settings/replaceMode/fragsCount/option: Use the frags count on this vehicle only in battle
settings/replaceMode/lifetimeFragsCount/option: Use the lifetime frags count on this vehicle
settings/replaceMode/moe/option: Use the MoE value on this vehicle
settings/replaceValue/label: New serial number value for player vehicle
settings/replaceValue/tooltip: Set the own vehicle serial number you wish.
settings/replaceValue/note: <b>Allowed values from 0 to 99999.</b>
