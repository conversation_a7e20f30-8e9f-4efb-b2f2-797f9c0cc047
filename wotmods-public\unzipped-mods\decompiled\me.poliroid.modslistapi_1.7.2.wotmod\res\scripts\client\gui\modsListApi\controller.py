# uncompyle6 version 3.9.2
# Python bytecode version base 2.7 (62211)
# Decompiled from: Python 3.12.6 (tags/v3.12.6:a4a2d2b, Sep  6 2024, 20:11:23) [MSC v.1940 64 bit (AMD64)]
# Embedded file name: python\gui\modsListApi\controller.py
# Compiled at: 2025-09-01 07:31:15
from .events import g_eventsManager
from .utils import get_logger
logger = get_logger(__name__)

class ApiLogicController(object):
    """
    The main controller for the ModsList API.
    Manages all modifications and their states.
    """

    def __init__(self):
        self.__modifications = dict()
        self.__isInLobby = False

    @property
    def modifications(self):
        """
        A dictionary of all registered modifications.
        """
        return self.__modifications

    @property
    def isInLobby(self):
        """
        A boolean indicating if the player is currently in the lobby.
        """
        return self.__isInLobby

    @isInLobby.setter
    def isInLobby(self, isInLobby):
        """
        Sets the lobby status.
        """
        self.__isInLobby = isInLobby

    @property
    def isModsExist(self):
        """
        A boolean indicating if any modifications are registered.
        """
        return len(self.__modifications)

    def addModification(self, id, name=None, description=None, icon=None, enabled=None, login=None, lobby=None, callback=None):
        """
        Adds a new modification or updates an existing one.
        """
        if id in self.__modifications.keys():
            return self.updateModification(id, name, description, icon, enabled, login, lobby, callback)
        else:
            if name is None or description is None or enabled is None or login is None or lobby is None or callback is None:
                logger.error('Method @addModification requires mandatory parameters [name, description, enabled, login, lobby, callback]')
                return
            from .data import ModificationItem
            modification = self.__modifications[id] = ModificationItem()
            modification.setData(id, name, description, icon, enabled, login, lobby, callback)
            return

    def updateModification(self, id, name=None, description=None, icon=None, enabled=None, login=None, lobby=None, callback=None):
        """
        Updates an existing modification.
        """
        if id not in self.__modifications:
            logger.error('Method @updateModification requires a ModificationItem instance. Use @addModification instead.')
            return
        modification = self.__modifications[id]
        modification.setData(id, name, description, icon, enabled, login, lobby, callback)

    def removeModification(self, id):
        """
        Removes a modification.
        """
        if id not in self.__modifications:
            logger.error('Method @removeModification requires a ModificationItem instance.')
            return
        del self.__modifications[id]
        g_eventsManager.onListUpdated()

    def alertModification(self, id):
        """
        Sets the alerting state for a modification.
        """
        if id not in self.__modifications:
            logger.error('Method @alertModification requires a ModificationItem instance. Check the id argument.')
            return
        modification = self.__modifications[id]
        modification.setAlerting(True)

    def clearModificationAlert(self, id):
        """
        Clears the alerting state for a modification.
        """
        if id not in self.__modifications:
            logger.error('Method @clearModificationAlert requires a ModificationItem instance. Check the id argument.')
            return
        modification = self.__modifications[id]
        modification.setAlerting(False)


g_controller = ApiLogicController()
