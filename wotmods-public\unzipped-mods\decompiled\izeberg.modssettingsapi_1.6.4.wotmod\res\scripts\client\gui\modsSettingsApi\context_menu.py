# uncompyle6 version 3.9.2
# Python bytecode version base 2.7 (62211)
# Decompiled from: Python 3.12.6 (tags/v3.12.6:a4a2d2b, Sep  6 2024, 20:11:23) [MSC v.1940 64 bit (AMD64)]
# Embedded file name: scripts/client/gui/modsSettingsApi/context_menu.py
# Compiled at: 2024-12-09 22:49:26
from gui.Scaleform.framework.managers.context_menu import AbstractContextMenuHand<PERSON>, registerHandlers as registerContextMenuHandlers
from helpers import dependency
from ..._constants import *
from .l10n import l10n
from .skeleton import IModsSettingsApiInternal

class HotkeyContextMenuHandler(AbstractContextMenuHandler):
    api = dependency.descriptor(IModsSettingsApiInternal)

    def __init__(self, cmProxy, ctx=None):
        self._linkage = None
        self._varName = None
        self._value = None
        super(HotkeyContextMenu<PERSON><PERSON><PERSON>, self).__init__(cmProxy, ctx, {(HOTKEY_OPTIONS.CLEAR_VALUE): 'clearValue', 
           (HOTKEY_OPTIONS.RESET_TO_DEFAULT_VALUE): 'resetToDefaultValue'})
        return

    def _initFlashValues(self, ctx):
        self._varName = ctx.varName
        self._linkage = ctx.linkage
        self._value = ctx.value

    def _clearFlashValues(self):
        self._linkage = None
        self._varName = None
        self._value = None
        return

    def clearValue(self):
        if self._linkage and self._varName:
            self.api.onHotkeyClear(self._linkage, self._varName)

    def resetToDefaultValue(self):
        if self._linkage and self._varName:
            self.api.onHotkeyDefault(self._linkage, self._varName)

    def _generateOptions(self, ctx=None):
        return [
         self._makeItem(HOTKEY_OPTIONS.RESET_TO_DEFAULT_VALUE, self.api.userSettings.get('buttonDefault') or l10n('buttons/default')),
         self._makeItem(HOTKEY_OPTIONS.CLEAR_VALUE, self.api.userSettings.get('buttonCleanup') or l10n('buttons/clear'), {'enabled': (len(self._value))})]


def getContextMenuHandlers():
    return (
     (
      HOTKEY_CONTEXT_MENU_HANDLER_ALIAS, HotkeyContextMenuHandler),)


registerContextMenuHandlers(*getContextMenuHandlers())
