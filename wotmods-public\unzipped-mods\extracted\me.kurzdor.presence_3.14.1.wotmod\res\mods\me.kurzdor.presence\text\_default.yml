# This file is solely for reference to allow adding support for any other language to Presence.
# If you wish to add localization, please contact the developer.
# Don't forget to remove first three lines from resulted translation. Thanks!
# Modification supports per-client string patching, i.e. if WG client has one event at current time 
# and Lesta client has other event, it will be overridden on Lesta client. 
# Use lesta/ or lestaPT/ prefixes for that.
# Translation author: Name <email>
# Translation version: 1
# Last updated: *date, example: dd/mm/yyyy*

common/missingL10n: Missing localization
statuses/gameLoading: Game loading
statuses/gameLoading/replay: Replay loading
statuses/login: Logging in
statuses/inLobby: In hangar
statuses/inLobby/squad: In platoon
statuses/inLobby/afk: In hangar (Idling)
statuses/inLobby/afk/squad: In platoon (Idling)
statuses/inQueue: In queue
statuses/inQueue/squad: Platoon in queue
statuses/arena/loading: Battle loading
statuses/arena/loading/replay: Replay loading
statuses/arena/waiting: Awaiting players
statuses/arena/waiting/replay: Watching replay
statuses/arena/prebattle: Waiting for battle start
statuses/arena/prebattle/replay: Watching replay
statuses/arena/battle: In battle
statuses/arena/battle/squad: Platoon in battle
statuses/arena/battle/replay: Watching replay
statuses/arena/killed: Spectating battle
statuses/arena/killed/replay: Watching replay
statuses/arena/inKillCam: Watching "Last Moment"
statuses/arena/win: Win!
statuses/arena/win/replay: Watching replay
statuses/arena/defeat: Defeat!
statuses/arena/defeat/replay: Watching replay
statuses/arena/draw: Draw!
statuses/arena/draw/replay: Watching replay
statuses/arena/leaving: Leaving battle
statuses/replayPaused: Watching replay (Paused)

# Game localization overrides in case of any event
