import zipfile
import os

# Directory containing the .wotmod files
mods_directory = "wotmods-public/unzipped-mods/mods/*******"

# Output directory for extracted files
output_directory = "wotmods-public/unzipped-mods/extracted"
os.makedirs(output_directory, exist_ok=True)

# Iterate through all .wotmod files in the directory
for file_name in os.listdir(mods_directory):
    if file_name.endswith(".wotmod"):
        file_path = os.path.join(mods_directory, file_name)
        print(f"Extracting {file_name}...")
        
        # Extract the .wotmod file
        with zipfile.ZipFile(file_path, 'r') as zip_ref:
            extract_path = os.path.join(output_directory, file_name)
            os.makedirs(extract_path, exist_ok=True)
            zip_ref.extractall(extract_path)

print("Extraction complete. All files are available in the 'extracted' directory.")
