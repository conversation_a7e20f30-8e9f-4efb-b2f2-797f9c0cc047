# uncompyle6 version 3.9.2
# Python bytecode version base 2.7 (62211)
# Decompiled from: Python 3.12.6 (tags/v3.12.6:a4a2d2b, Sep  6 2024, 20:11:23) [MSC v.1940 64 bit (AMD64)]
# Embedded file name: scripts/client/gui/modsSettingsApi/__init__.py
# Compiled at: 2025-08-19 02:47:47
__author__ = 'Renat Iliev'
__copyright__ = 'Copyright 2025, Wargaming'
__credits__ = ['<PERSON><PERSON><PERSON>', 'Renat Iliev', '<PERSON>']
__license__ = 'CC BY-NC-SA 4.0'
__version__ = '1.6.4'
__maintainer__ = '<PERSON>'
__email__ = '<EMAIL>'
__doc__ = 'https://wiki.wargaming.net/ru/ModsettingsAPI'
import templates
from .api import ModsSettingsApi
from .skeleton import IModsSettingsApi
from ._constants import SPECIAL_KEYS
from .utils import getDependencyManager
__all__ = ('g_modsSettingsApi', 'IModsSettingsApi', 'templates', 'SPECIAL_KEYS')

class _ModsSettingsApi(IModsSettingsApi):
    """
        API доступа к меню настроек
        """

    def __init__(self):
        super(_ModsSettingsApi, self).__init__()
        self.__instance = ModsSettingsApi()
        manager = getDependencyManager()
        if manager is not None:
            manager.addInstance(IModsSettingsApi, self)
        return

    def saveModData(self, linkage, version, data):
        """ Сохранение данных мода
                :param linkage: Идентификатор
                :param version: Версия данных
                :param data: Данные для сохранения
                :return: Сохраненные настройки
                """
        return self.__instance.saveModData(linkage, version, data)

    def getModData(self, linkage, version, default):
        """ Получение данных мода
                Eсли запрошенная версия не соответствует сохраненной, будут сохранены и возвращены стандартные данные
                :param linkage: Идентификатор
                :param version: Версия данных
                :param default: Стандартные данные
                :return: Сохраненные настройки
                """
        return self.__instance.getModData(linkage, version, default)

    def setModTemplate(self, linkage, template, callback, buttonHandler=None):
        """ Инициализация настроек
                :param linkage: Идентификатор настроек
                :param template: Шаблон настроек
                :param callback: Функция-обработчик новых настроек
                :param buttonHandler: Функция-обработчик нажатий на кнопку
                :return: Сохраненные настройки
                """
        return self.__instance.setModTemplate(linkage, template, callback, buttonHandler)

    def registerCallback(self, linkage, callback, buttonHandler=None):
        """ Регистрация функций-обработчиков вызова
                :param linkage: Идентификатор настроек
                :param callback: Функция-обработчик новых настроек
                :param buttonHandler: Функция-обработчик нажатий на кнопку
                """
        return self.__instance.registerCallback(linkage, callback, buttonHandler)

    def getModSettings(self, linkage, template):
        """ Получение сохраненных настроек
                :param linkage: Идентификатор настроек
                :param template: Шаблон настроек
                :return: Сохраненные настройки, если таковых нет (либо есть, но устаревшие) - None
                """
        return self.__instance.getModSettings(linkage, template)

    def updateModSettings(self, linkage, newSettings):
        """ Изменение сохраненных настроек
                :param linkage: Идентификатор настроек
                :param newSettings: Новые настройки
                """
        return self.__instance.updateModSettings(linkage, newSettings)

    def checkKeyset(self, keyset):
        """ Проверка нажатия клавиш
                :param keyset: Набор клавиш для проверки
                :return: bool
                """
        return self.__instance.checkKeyset(keyset)


g_modsSettingsApi = ModsSettingsApi()
