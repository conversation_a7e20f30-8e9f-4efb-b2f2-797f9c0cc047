# wotmods-public

<p align="center">
	&bull; <a href="#en">EN</a> | &bull; <a href="#ru">RU</a> 
</p>

## EN
Modifications for World of Tanks

### Installation

1. Download .zip archive from modification folder
2. Unpack archive to the game root folder

### List of modifications

- [Advanced Consumables Panel](./advancedtankcarousel/): Enhance your consumables panel in battle
- [Advanced Personal Efficiency](./advancedpersonalefficiency/): Extends default personal efficiency counters near damage panel
- [Advanced Tank Carousel (WIP)](./advancedtankcarousel/): Enhance your tank carousel
- [Auto Equipment Return](./autoequipmentreturn/): Auto equipment return with WoT Plus
- [Battle Equipment](./battleequipment/): Show current equipment preset in battle
- [Elite Levels Remover](./elitelevelsremover/): Visually removes Elite Levels (i.e. Prestige) system from every possible place in game
- [Extended Zoom Switcher](./extendedzoomswitcher/): Quick extended zoom switcher by hotkey
- [Free XP Helper](./freexphelper/): Helper for free XP exchange window to prevent exchanging XP for non-fully researched or progressed vehicles
- [Presence](./presence/): Show extended game status in Discord RPC
- [Outlining Colors](./outliningcolors/): Change outlining and fill colors when pointing at the tank
- [Permanently Destroyed](./permanentlydestroyed/): Persist positions of destroyed vehicle icons on minimap
- [Postmortem Fix](./postmortemfix/): Fixes for postmortem modes
- [Serial Number Changer](./serialnumberchanger/): Change your serial number to any value: from any value to frags count or current MoE percent
- [Vehicle State](./vehiclestate/): Отображение расширенной информации про состояние техники игрока - очки здоровья до информации про заряженный снаряд в данный момент

### Thanks to:

Special thanks for people that gave any help and for their work:

- https://github.com/poliroid
- https://github.com/spoter
- https://github.com/StranikS-Scan
- https://github.com/PolyacovYury
- https://github.com/Armagomen
- https://github.com/CH4MPi
- https://github.com/IzeBerg

and those whom I could forget

## RU
Модификации для клиента World of Tanks

### Установка модов

1. Скачать .zip архив из папки модификации
2. Распаковать данный архив в корневую папку с игрой

### Список модификаций

- [Advanced Consumables Panel](./advancedtankcarousel/): Расширение возможностей панели расходников в ангаре
- [Advanced Personal Efficiency](./advancedpersonalefficiency/): Расширение возможностей стандартных счетчиков личной эффективности рядом с панелью повреждений
- [Advanced Tank Carousel (В РАЗРАБОТКЕ)](./advancedtankcarousel/): Расширение возможностей карусели танков в ангаре
- [Auto Equipment Return](./autoequipmentreturn/): Автоматическое возвращение оборудования с помощью WoT Plus
- [Battle Equipment](./battleequipment/): Отображение текущего пресета оборудования в бою
- [Elite Levels Remover](./elitelevelsremover/): Визуально убирает систему уровней элитности со всех возможных мест в игре
- [Extended Zoom Switcher](./extendedzoomswitcher/): Быстрое переключение расширеного зума
- [Free XP Helper](./freexphelper/): Запрет на перевод свободного опыта, если техника не полностью прокачена либо полевая модернизация не прокачана полностью
- [Presence](./presence/): Отображение расширеного статуса игрока в Discord RPC
- [Outlining Colors](./outliningcolors/): Изменение цветов обводки и заливки при наведении на танк
- [Permanently Destroyed](./permanentlydestroyed/): Сохранение позиций иконок уничтоженных танков на миникарте
- [Postmortem Fix](./postmortemfix/): Исправления для режима посмертия
- [Serial Number Changer](./serialnumberchanger/): Изменение серийного номера на любое другое значение: от любого другого значения до количества фрагов и отметки до боя
- [Vehicle State](./vehiclestate/): Отображение расширенной информации про состояние техники игрока - очки здоровья до информации про заряженный снаряд в данный момент

### Благодарности

Отдельная благодарность данным людям за различную помощь, а также за другие труды:

- https://github.com/poliroid
- https://github.com/spoter
- https://github.com/StranikS-Scan
- https://github.com/PolyacovYury
- https://github.com/Armagomen
- https://github.com/CH4MPi
- https://github.com/IzeBerg

а также те, которых я забыл упомянуть
