import os
import subprocess

# Directory containing the extracted mods
mods_directory = "wotmods-public/unzipped-mods/extracted"

# Output directory for decompiled files
output_directory = "wotmods-public/unzipped-mods/decompiled"
os.makedirs(output_directory, exist_ok=True)

# Full path to uncompyle6 executable
uncompyle6_path = "C:/Users/<USER>/AppData/Roaming/Python/Python312/Scripts/uncompyle6.exe"

# Function to decompile .pyc files
def decompile_pyc(file_path, output_dir):
    try:
        subprocess.run([
            uncompyle6_path,
            "-o",
            output_dir,
            file_path
        ], check=True)
        print(f"Decompiled: {file_path}")
    except subprocess.CalledProcessError as e:
        print(f"Failed to decompile {file_path}: {e}")

# Walk through the mods directory to find .pyc files
for root, dirs, files in os.walk(mods_directory):
    for file in files:
        if file.endswith(".pyc"):
            file_path = os.path.join(root, file)
            relative_path = os.path.relpath(root, mods_directory)
            decompile_output_dir = os.path.join(output_directory, relative_path)
            os.makedirs(decompile_output_dir, exist_ok=True)
            decompile_pyc(file_path, decompile_output_dir)

print("Decompilation complete. Decompiled files are available in the 'decompiled' directory.")
