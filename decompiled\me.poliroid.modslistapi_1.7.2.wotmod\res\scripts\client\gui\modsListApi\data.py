# uncompyle6 version 3.9.2
# Python bytecode version base 2.7 (62211)
# Decompiled from: Python 3.12.6 (tags/v3.12.6:a4a2d2b, Sep  6 2024, 20:11:23) [MSC v.1940 64 bit (AMD64)]
# Embedded file name: python\gui\modsListApi\data.py
# Compiled at: 2025-08-31 21:59:02
import BigWorld, ResMgr
from ids_generators import SequenceIDGenerator
from ._constants import DEFAULT_MOD_ICON
from .controller import g_controller
from .lang import l10n
from .events import g_eventsManager
from .utils import format_description, get_logger
__all__ = ('g_dataProvider', 'ModificationItem')
logger = get_logger(__name__)

class _DataProvider(object):
    """
    Provides data for the ModsList API views.
    """

    @property
    def modsData(self):
        """
        Generates and returns data for all available modifications.
        The return value is represented by ModsListStaticDataVO in ActionScript.
        """
        return self._generateModsData()

    @property
    def staticData(self):
        """
        Generates and returns static data for the ModsList views.
        The return value is represented by ModsListModsVO in ActionScript.
        """
        return self._generateStaticData()

    @property
    def alertsCount(self):
        """
        Calculates and returns the number of modifications with alerts.
        """
        return sum(int(item.alerting) for item in g_controller.modifications.values())

    @staticmethod
    def _generateModsData():
        """
        Generates a dictionary of modifications data.
        """
        result = list()
        for item in g_controller.modifications.values():
            if item.available:
                result.append(item.dpData)

        if result:
            result = sorted(result, key=(lambda item: item.get('id')))
        return {'mods': result}

    @staticmethod
    def _generateStaticData():
        """
        Generates a dictionary of static data.
        """
        result = {'titleLabel': (l10n('title')), 
           'tooltipLabel': ('{HEADER}%s{/HEADER}{BODY}%s{/BODY}' % (l10n('title'), l10n('description'))), 
           'closeButtonVisible': True}
        return result


g_dataProvider = _DataProvider()
IDGenerator = SequenceIDGenerator()

class ModificationItem(object):
    """
    Represents a single modification item.
    """

    def __init__(self):
        self.__numID = IDGenerator.next()
        self.__stringID = ''
        self.__alerting = False
        self.__callback = lambda : logger.warning('handler for "%s" not installed' % self.__stringID)
        self.__enabled = False
        self.__availableInLobby = False
        self.__availableInLogin = False
        self.__name = ''
        self.__description = ''
        self.__icon = ''
        g_eventsManager.invokeModification += self.__invokeModification

    @property
    def uniqueID(self):
        """
        The unique numeric ID of the modification.
        """
        return self.__numID

    @property
    def available(self):
        """
        Checks if the modification is currently available.
        """
        return self.__availabilityCheck()

    @property
    def dpData(self):
        """
        Generates data for the data provider.
        """
        return self.__genDataForDP()

    @property
    def alerting(self):
        return self.__alerting

    def setData(self, id, name, description, icon, enabled, login, lobby, callback):
        """
        Sets the data for the modification item.
        """
        if id is not None:
            self.__stringID = id
        if enabled is not None:
            self.__enabled = enabled
        if lobby is not None:
            self.__availableInLobby = lobby
        if login is not None:
            self.__availableInLogin = login
        if name is not None:
            self.__name = name
        if description is not None:
            self.__description = format_description(description)
        if callback is not None:
            self.__callback = callback
        if icon:
            self.__icon = self.__fixModIcon(icon)
        g_eventsManager.onListUpdated()
        return

    def __fixModIcon(self, path):
        """
        Validates and fixes the modification icon path.
        """
        if not path or not ResMgr.isFile(path):
            return DEFAULT_MOD_ICON
        return '../../%s' % path

    def setAlerting(self, isAlerting):
        """
        Sets the alerting state for the modification.
        """
        self.__alerting = isAlerting
        g_eventsManager.onListUpdated()

    def __availabilityCheck(self):
        """
        Checks if the modification should be visible.
        """
        result = False
        if g_controller.isInLobby and self.__availableInLobby:
            result = True
        if not g_controller.isInLobby and self.__availableInLogin:
            result = True
        return result

    def __genDataForDP(self):
        """
        Generates a dictionary of data for the data provider.
        """
        result = {'id': (self.__numID), 
           'isEnabled': (self.__enabled), 
           'isAlerting': (self.__alerting), 
           'nameLabel': (self.__name), 
           'tooltipLabel': (self.__description), 
           'icon': (self.__icon)}
        return result

    def __invokeModification(self, modificationID):
        """
        Invokes the modification's callback.
        """
        if modificationID != self.__numID:
            return
        if callable(self.__callback):
            self.__alerting = False
            BigWorld.callback(0, self.__callback)
