# scope: vehiclestate

settings/modDisplayName: Розширена інформація про стан техніки гравця
settings/healthInfo/template/label: Шаблон для форматування інформації про ХП техніки
settings/healthInfo/template/tooltip: Доступні макроси:\n\n- %(health)s - поточний рівень ХП техніки гравця\n- %(c:health)s - колір в залежності від поточного рівня ХП гравця\n- %(maxHealth)s - максимальний рівень ХП з урахуванням усіх факторів (покращена закалка, польова модернізація та інше) техніки гравця\n- %(healthPercentage)s - поточний процент ХП техніки гравця
settings/shellInfo/template/label: Шаблон для форматування інформації про поточний снаряд
settings/shellInfo/template/tooltip: Доступні макроси:\n\n- %(shellDamage)s - шкода зарядженого снаряда\n- %(shellDamageDispersion)s - розкид шкоди зарядженого снаряда (з урахуванням +-25%)\n- %(shellPenetration)s - пробивання зарядженого снаряда\n- %(shellPenetrationDispersion)s - розкид пробивання зарядженого снаряда (з урахуванням +-25%)\n- %(shellType)s - поточний тип зарядженого снаряда\n- %(shellKind)s - поточний тип зарядженого снаряда (звичайний, преміум або станящий)\n- %(c:shellKind)s - колір поточного типу зарядженого снаряда (звичайний, преміум або станящий)\n- %(shellSpeed)s - швидкість польоту зарядженого снаряда\n- %(shellCaliber)s - калібр зарядженого снаряда\n- %(gunElevation)s - максимальний верхній кут вертикальної наведення танка\n- %(gunDepression)s - максимальний нижній кут вертикальної наведення танка\n- %(gunPitchLimits)s - кути вертикального наведення даного танку
settings/prettyNumberFormat/label: Увімкнути красивий формат чисел
settings/prettyNumberFormat/tooltip: Якщо дана опція увімкнена, то ви будете бачити формат чисел виду 1 400 або 1,400 замість 1400 в залежності від ваших системних налашутвань.
settings/align/label: Вирівнювання тексту панелі
settings/align/tooltip: Визначає вирівнювання тексту панелі.
settings/align/left/option: Зліва
settings/align/center/option: По центру
settings/align/right/option: Справа
settings/aimingTimer/enabled/label: Увімкнути таймер зведення
settings/aimingTimer/enabled/tooltip: Якщо увімкнено, модифікація буде відображати час, що залишається до повного зведення гармати в бою.
settings/aimingTimer/template/label: Шаблон для форматування таймера зведення
settings/aimingTimer/template/tooltip: Доступні макроси:\n\n- %(aimingRemainingTime)s - час, що залишається до повного зведення в бою\n- %(с:aimingStatus)s - колір в залежності від статусу зведення (зведення, майже зведений та зведений)\n- %(aimingProgress)s - прогрес зведення гармати у відсотках
settings/aimingTimer/colors/aiming/label: Колір: Не зведений
settings/aimingTimer/colors/aiming/tooltip: Колір тексту, коли гармата в процесі зведення.
settings/aimingTimer/colors/almostDone/label: Колір: Майже зведений
settings/aimingTimer/colors/almostDone/tooltip: Колір тексту, коли гармата майже звелась.
settings/aimingTimer/colors/done/label: Колір: Зведений
settings/aimingTimer/colors/done/tooltip: Колір тексту, коли гармата повністю зведена.
settings/aimingTimer/almostDoneThreshold/label: Порогове значення "майже зведений"
settings/aimingTimer/almostDoneThreshold/tooltip: Визначає порогове значення прогресу зведення (у відсотках), коли таймер зведення отримає статус "майже зведений".
settings/coolingDelayTimer/enabled/label: Увімкнути таймер охолодження гармати
settings/coolingDelayTimer/enabled/tooltip: Якщо увімкнено, модифікація буде відображати час охолодження гармати для танків, які підтримують механіку подвійного зведення (охолодження гармати), як-от японскі важкі танки.
settings/hotkeys/toggle/label: Гаряча клавіша: Перемикач моду
settings/hotkeys/toggle/tooltip: Виберіть гарячу клавішу для того, аби вимкнути або увімкнути модифікацію в бою
battle/aimingTimer/done: Зведений!
battle/messages/enabled: Розширена інформація про стан техніки гравця: ВКЛ
battle/messages/disabled: Розширена інформація про стан техніки гравця: ВИКЛ
battle/tip/text: Панель можливо перетягнути мишею.
battle/shellKind/normal: Звичайний
battle/shellKind/premium: Преміум
battle/shellKind/stun: Станящий
