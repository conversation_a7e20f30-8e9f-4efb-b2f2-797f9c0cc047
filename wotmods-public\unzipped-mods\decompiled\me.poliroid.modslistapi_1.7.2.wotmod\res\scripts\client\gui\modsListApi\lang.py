# uncompyle6 version 3.9.2
# Python bytecode version base 2.7 (62211)
# Decompiled from: Python 3.12.6 (tags/v3.12.6:a4a2d2b, Sep  6 2024, 20:11:23) [MSC v.1940 64 bit (AMD64)]
# Embedded file name: python\gui\modsListApi\lang.py
# Compiled at: 2025-08-31 18:22:54
from helpers import getClientLanguage
from ._constants import LANGUAGE_DEFAULT, LANGUAGE_FALLBACK, LANGUAGE_FILES
from .utils import cache_result, parse_localization_file, vfs_dir_list_files

class Localization(object):
    """
    A class to handle localization.
    """

    def __init__(self, locale_folder, default=LANGUAGE_DEFAULT, fallback=LANGUAGE_FALLBACK):
        """
        Initializes the localization class.

        :param locale_folder: The folder containing the localization files.
        :param default: The default language.
        :param fallback: A dictionary of fallback languages.
        """
        self.languages = {}
        for file_name in vfs_dir_list_files(locale_folder):
            if not file_name.endswith('.yml'):
                continue
            file_path = '%s/%s' % (locale_folder, file_name)
            lang_data = parse_localization_file(file_path)
            if lang_data:
                lang_code = file_name.replace('.yml', '')
                self.languages[lang_code] = lang_data

        self._ui_default = default
        client_language = getClientLanguage()
        self._client_default = default
        if client_language in fallback:
            self._client_default = fallback[0]
        self.language = {}
        if client_language in self.languages:
            self.language = self.languages[client_language]
        elif self._client_default in self.languages:
            self.language = self.languages[self._client_default]
        else:
            self.language = self.languages.get(self._ui_default, {})

    @cache_result
    def __call__(self, locale_key):
        """
        Gets the localized string for the given key.

        :param locale_key: The key to localize.
        :return: The localized string.
        """
        if locale_key in self.language:
            return self.language[locale_key]
        if locale_key in self.languages[self._client_default]:
            return self.languages[self._client_default][locale_key]
        if locale_key in self.languages[self._ui_default]:
            return self.languages[self._ui_default][locale_key]
        return locale_key

    @cache_result
    def get_sentences(self):
        """
        Gets a dictionary of all localized strings.
        """
        result = {}
        for k, v in self.languages[self._ui_default].items():
            result[k] = v

        for k, v in self.languages[self._client_default].items():
            result[k] = v

        for k, v in self.language.items():
            result[k] = v

        return result


l10n = Localization(LANGUAGE_FILES)
