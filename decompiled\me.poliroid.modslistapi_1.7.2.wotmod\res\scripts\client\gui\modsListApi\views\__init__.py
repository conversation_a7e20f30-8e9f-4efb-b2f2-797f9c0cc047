# uncompyle6 version 3.9.2
# Python bytecode version base 2.7 (62211)
# Decompiled from: Python 3.12.6 (tags/v3.12.6:a4a2d2b, Sep  6 2024, 20:11:23) [MSC v.1940 64 bit (AMD64)]
# Embedded file name: python\gui\modsListApi\views\__init__.py
# Compiled at: 2025-08-31 19:24:03
"""
This package contains the view definitions for the ModsList API.
"""
from frameworks.wulf import WindowLayer
from gui.Scaleform.framework import ScopeTemplates
from gui.Scaleform.framework import g_entitiesFactories, ComponentSettings, GroupedViewSettings, ViewSettings, ScopeTemplates
from gui.Scaleform.framework.entities.View import View
from .._constants import MODS_LIST_BUTTON_POPOVER, MODS_LIST_BUTTON_VIEW, MODS_LIST_BUTTON_INJECT
from .modsPopover import ModsListPopoverView
from .modsButton import ModsButtonInjectComponent
g_entitiesFactories.addSettings(GroupedViewSettings(MODS_LIST_BUTTON_POPOVER, ModsListPopoverView, 'modsListPopover.swf', WindowLayer.WINDOW, MODS_LIST_BUTTON_POPOVER, MODS_LIST_BUTTON_POPOVER, ScopeTemplates.WINDOW_VIEWED_MULTISCOPE))
g_entitiesFactories.addSettings(ViewSettings(MODS_LIST_BUTTON_VIEW, View, 'modsListButton.swf', WindowLayer.WINDOW, None, ScopeTemplates.GLOBAL_SCOPE))
g_entitiesFactories.addSettings(ComponentSettings(MODS_LIST_BUTTON_INJECT, ModsButtonInjectComponent, ScopeTemplates.DEFAULT_SCOPE))
