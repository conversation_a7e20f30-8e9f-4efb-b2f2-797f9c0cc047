# scope: vehiclestate

settings/modDisplayName: Vehicle State
settings/healthInfo/template/label: Health info format template
settings/healthInfo/template/tooltip: Available macroses:\n\n- %(health)s - current player's vehicle HP level\n- %(c:health)s - color depending of current player's vehicle HP level\n- %(maxHealth)s - max player's vehicle HP level including all factors (improved hardening, field modernization, etc.)\n- %(healthPercentage)s - current player's vehicle HP percentage
settings/shellInfo/template/label: Shell info format template
settings/shellInfo/template/tooltip: Available macroses:\n\n- %(shellDamage)s - current loaded shell damage\n- %(shellDamageDispersion)s - current loaded shell damage dispersion\n- %(shellPenetration)s - current loaded shell penetration\n- %(shellPenetrationDispersion)s - current shell penetration with +-25% dispersion\n- %(shellType)s - current shell type\n- %(shellKind)s - current shell kind (normal, premium or stun)\n- %(c:shellKind)s - current shell type color (normal, premium or stun)\n- %(shellSpeed)s - current shell speed\n- %(shellCaliber)s - current shell caliber\n- %(gunElevation)s - current vehicle's gun elevation\n- %(gunDepression)s - current vehicle's gun depression\n- %(gunPitchLimits)s - current vehicle's gun elevation and depression
settings/prettyNumberFormat/label: Enable pretty number format
settings/prettyNumberFormat/tooltip: If enabled, you will get formatted number, i.e. 1 400 or 1,400 instead of 1400 depending on system settings.
settings/align/label: Panel text align
settings/align/tooltip: Defines align of panel's text.
settings/align/left/option: Left
settings/align/center/option: Center
settings/align/right/option: Right
settings/aimingTimer/enabled/label: Enable aiming timer
settings/aimingTimer/enabled/tooltip: If enabled, mod will display remaining time to fully aim in battle.
settings/aimingTimer/template/label: Aiming timer format template
settings/aimingTimer/template/tooltip: Available macroses:\n\n- %(aimingRemainingTime)s - remaining time to fully aim in seconds\n- %(с:aimingStatus)s - color depending on current aiming status (aiming, almost done and done)\n- %(aimingProgress)s - aiming progress in percents
settings/aimingTimer/colors/aiming/label: Color: Aiming
settings/aimingTimer/colors/aiming/tooltip: Color of text when the aiming is in process.
settings/aimingTimer/colors/almostDone/label: Color: Aiming almost done
settings/aimingTimer/colors/almostDone/tooltip: Color of text when the aiming process almost finished.
settings/aimingTimer/colors/done/label: Color: Aiming done
settings/aimingTimer/colors/done/tooltip: Color of text when the aiming process finished.
settings/aimingTimer/almostDoneThreshold/label: Aiming timer almost done threshold
settings/aimingTimer/almostDoneThreshold/tooltip: Define a percent progress threshold when almost done status of aiming is given to aiming timer.
settings/coolingDelayTimer/enabled/label: Enable gun cooling delay timer
settings/coolingDelayTimer/enabled/tooltip: If enabled, mod will display cooling delay time for vehicles (japanese heavy tanks) that support double aiming or gun cooling mechanic.
settings/hotkeys/toggle/label: Hotkey: Toggle mod
settings/hotkeys/toggle/tooltip: Select hotkey to toggle mod in battle
battle/aimingTimer/done: Ready!
battle/messages/enabled: Vehicle State: ON
battle/messages/disabled: Vehicle State: OFF
battle/tip/text: Move the panel with mouse.
battle/shellKind/normal: Normal
battle/shellKind/premium: Premium
battle/shellKind/stun: Stun
