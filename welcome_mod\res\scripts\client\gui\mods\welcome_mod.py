# Import the necessary modules
import BigWorld

# Define the mod class
class WelcomeMod:
    def __init__(self):
        # Display the welcome message when the mod is loaded
        BigWorld.callback(3.0, self.show_welcome_message)

    def show_welcome_message(self):
        try:
            # Try to import and use SystemMessages for notification
            from gui.SystemMessages import pushMessage, SM_TYPE
            pushMessage('COME STAI?', SM_TYPE.Information)
            print("Welcome mod loaded and notification sent!")
        except ImportError:
            try:
                # Alternative approach using MessagesController
                from messenger import MessagesController
                from messenger.m_constants import SCH_CLIENT_MSG_TYPE
                MessagesController.g_instance.onReceiveMessage(
                    SCH_CLIENT_MSG_TYPE.SYS_MSG_TYPE.information,
                    'COME STAI?',
                    {}
                )
                print("Welcome mod loaded with MessagesController!")
            except Exception as e2:
                print("Welcome mod fallback error:", str(e2))
                # Final fallback - just print to console
                print("COME STAI? (console only)")
        except Exception as e:
            print("Welcome mod error:", str(e))
            print("COME STAI? (console only)")

# Instantiate the mod
WelcomeMod()