# uncompyle6 version 3.9.2
# Python bytecode version base 2.7 (62211)
# Decompiled from: Python 3.12.6 (tags/v3.12.6:a4a2d2b, Sep  6 2024, 20:11:23) [MSC v.1940 64 bit (AMD64)]
# Embedded file name: scripts/client/gui/modsSettingsApi/api.py
# Compiled at: 2025-04-30 08:44:46
import os, functools, copy, logging, BigWorld, cPickle, Event
from gui.modsListApi import g_modsListApi
from ..._constants import *
from .l10n import l10n
from ...context_menu import *
from .hotkeys import HotkeysController
from .view import loadView
from .skeleton import IModsSettingsApiInternal
from .utils import deprecated, getDependencyManager, jsonLoad, jsonDump
_logger = logging.getLogger(__name__)

class ModsSettingsApi(IModsSettingsApiInternal):

    def __init__(self):
        super(ModsSettings<PERSON>pi, self).__init__()
        self.__saveCallbackID = None
        self.activeMods = set()
        self.state = {'settings': {}, 'templates': {}, 'storage': {}}
        self.userSettings = {}
        self.hotkeys = HotkeysController(self)
        self.onWindowOpened = Event.Event()
        self.onWindowClosed = Event.Event()
        self.onHotkeysUpdated = Event.Event()
        self.onButtonClicked = Event.Event()
        self.onSettingsChanged = Event.Event()
        self.loadSettings()
        self.loadState()
        g_modsListApi.addModification(id=MOD_ID, name=self.userSettings.get('modsListApiName') or l10n('name'), description=self.userSettings.get('modsListApiDescription') or l10n('description'), icon=self.userSettings.get('modsListApiIcon') or MOD_ICON, enabled=True, login=True, lobby=True, callback=functools.partial(loadView, self))
        manager = getDependencyManager()
        if manager is not None:
            manager.addInstance(IModsSettingsApiInternal, self)
        return

    def loadSettings(self):
        if not os.path.exists(USER_SETTINGS_PATH):
            return
        try:
            with open(USER_SETTINGS_PATH, 'rb') as settingsFile:
                self.userSettings = jsonLoad(settingsFile)
        except Exception:
            _logger.exception('Error occured when trying to load user settings!')

    def loadState(self):
        if not os.path.exists(STATE_FILE_PATH):
            self.saveState()
            return
        try:
            with open(STATE_FILE_PATH, 'rb') as stateFile:
                self.state = jsonLoad(stateFile)
                self.state.setdefault('storage', {})
                self.__migrateState()
        except Exception:
            _logger.exception('Error occured when trying to load state!')

    def __migrateState(self):
        if 'data' in self.state:
            data = self.state.pop('data')
            self.state['storage'] = data

    def saveState(self):
        if self.__saveCallbackID is None:
            self.__saveCallbackID = BigWorld.callback(0.0, self.__save)
        return

    def __save(self):
        self.__saveCallbackID = None
        try:
            stateDir = os.path.dirname(STATE_FILE_PATH)
            if not os.path.isdir(stateDir):
                os.makedirs(stateDir)
        except Exception:
            _logger.exception('Error occured when trying to recreate folder structure for state file!')

        try:
            with open(STATE_FILE_PATH, 'wb') as stateFile:
                stateFile.write(jsonDump(self.state, True))
        except Exception:
            _logger.exception('Error occured when trying to save state!')

        return

    def clearState(self):
        for linkage in self.state['templates'].keys():
            if linkage not in self.activeMods:
                del self.state['templates'][linkage]
                del self.state['settings'][linkage]

    def setModTemplate(self, linkage, template, callback, buttonHandler=None):
        try:
            self.activeMods.add(linkage)
            currentTemplate = self.state['templates'].get(linkage)
            if not currentTemplate or self.compareTemplates(template, currentTemplate):
                self.state['templates'][linkage] = template
                self.state['settings'][linkage] = self.getSettingsFromTemplate(template)
                self.saveState()
            self.onSettingsChanged += callback
            if buttonHandler is not None:
                self.onButtonClicked += buttonHandler
            return self.getModSettings(linkage, self.state['templates'][linkage])
        except Exception:
            _logger.exception('Error occured when trying to register mod template!')

        return

    def getModSettings(self, linkage, template):
        result = None
        if template:
            currentTemplate = self.state['templates'].get(linkage)
            if currentTemplate:
                if not self.compareTemplates(template, currentTemplate):
                    result = self.state['settings'].get(linkage)
                self.activeMods.add(linkage)
        return result

    def registerCallback(self, linkage, callback, buttonHandler=None):
        self.activeMods.add(linkage)
        self.onSettingsChanged += callback
        if buttonHandler is not None:
            self.onButtonClicked += buttonHandler
        return

    def getModData(self, linkage, version, default):
        storage = self.state['storage']
        if linkage not in storage or storage[linkage]['version'] != version:
            self.saveModData(linkage, version, default)
        return cPickle.loads(storage[linkage]['data'])

    def saveModData(self, linkage, version, data):
        self.state['storage'][linkage] = {'version': version, 
           'data': (cPickle.dumps(data, -1))}
        self.saveState()

    def updateModSettings(self, linkage, newSettings):
        self.state['settings'][linkage] = newSettings
        self.onSettingsChanged(linkage, newSettings)

    def checkKeyset(self, keys):
        return self.hotkeys.checkKeyset(keys)

    @deprecated('checkKeyset')
    def checkKeySet(self, keys):
        return self.checkKeyset(keys)

    def compareTemplates(self, newTemplate, oldTemplate):
        if 'settingsVersion' in newTemplate and 'settingsVersion' in oldTemplate:
            return newTemplate['settingsVersion'] > oldTemplate['settingsVersion']
        return jsonDump(newTemplate, True) != jsonDump(oldTemplate, True)

    def getSettingsFromTemplate(self, template):
        result = dict()
        if 'enabled' in template:
            result['enabled'] = template['enabled']
        for column in COLUMNS:
            if column in template:
                result.update(self.getSettingsFromColumn(template[column]))

        return result

    def getSettingsFromColumn(self, column):
        result = dict()
        for component in column:
            if 'varName' in component and 'value' in component:
                result[component['varName']] = component['value']

        return result

    def generateSettingsData(self):
        templates = []
        linkages = sorted(self.state['templates'], key=str.lower)
        for linkage in linkages:
            template = copy.deepcopy(self.state['templates'][linkage])
            settings = self.getModSettings(linkage, template)
            template['linkage'] = linkage
            if 'enabled' in template:
                template['enabled'] = settings['enabled']
            for column in COLUMNS:
                if column in template:
                    for component in template[column]:
                        if 'varName' in component:
                            component['value'] = settings[component['varName']]

            templates.append(template)

        return templates

    def getAllHotkeys(self):
        return self.hotkeys.getAllHotkeys()

    def onHotkeyStartAccept(self, linkage, varName):
        return self.hotkeys.startAccept(linkage, varName)

    def onHotkeyStopAccept(self, linkage, varName):
        return self.hotkeys.stopAccept()

    def onHotkeyDefault(self, linkage, varName):
        return self.hotkeys.reset(linkage, varName)

    def onHotkeyClear(self, linkage, varName):
        return self.hotkeys.clear(linkage, varName)
