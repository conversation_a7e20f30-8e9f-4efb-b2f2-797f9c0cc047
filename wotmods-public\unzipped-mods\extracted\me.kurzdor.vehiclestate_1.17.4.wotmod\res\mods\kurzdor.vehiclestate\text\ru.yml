# scope: vehiclestate

settings/modDisplayName: Расширенная информация об состоянии техники игрока
settings/healthInfo/template/label: Шаблон для форматирования информации про ХП техники
settings/healthInfo/template/tooltip: Доступные макросы:\n\n- %(health)s - текущий уровень ХП техники игрока\n- %(c:health)s - цвет в зависимости от текущего уровня ХП игрока\n- %(maxHealth)s - максимальный уровень ХП с учётом всех факторов (улучшенная закалка, полевая модернизация и прочее) техники игрока\n- %(healthPercentage)s - текущий процент ХП техники игрока
settings/shellInfo/template/label: Шаблон для форматирования информации про текущий снаряд
settings/shellInfo/template/tooltip: Доступные макросы:\n\n- %(shellDamage)s - урон заряженного снаряда\n- %(shellDamageDispersion)s - разброс урона заряженного снаряда (с учётом +-25%)\n- %(shellPenetration)s - пробитие заряженного снаряда\n- %(shellPenetrationDispersion)s - разброс пробития заряженного снаряда (с учётом +-25%)\n- %(shellType)s - текущий тип заряженного снаряда\n- %(shellKind)s - текущий тип заряженного снаряда (обычный, премиум или станящий)\n- %(c:shellKind)s - цвет текущего типа заряженного снаряда (обычный, премиум или станящий)\n- %(shellSpeed)s - скорость полёта заряженного снаряда\n- %(shellCaliber)s - калибр заряженного снаряда\n- %(gunElevation)s - максимальный верхний угол вертикальной наводки танка\n- %(gunDepression)s - максимальный нижний угол вертикальной наводки танка\n- %(gunPitchLimits)s - углы вертикальной наводки танка
settings/prettyNumberFormat/label: Включить красивый формат чисел
settings/prettyNumberFormat/tooltip: Если данная опция включена, то вы будете видеть формат чисел вида 1 400 либо 1,400 вместо 1400 в зависимости от системных настроек.
settings/align/label: Выравнивание текста панели
settings/align/tooltip: Определяет выравнивание текста панели.
settings/align/left/option: Слева
settings/align/center/option: По центру
settings/align/right/option: Справа
settings/aimingTimer/enabled/label: Включить таймер сведения
settings/aimingTimer/enabled/tooltip: Если включено, мод будет отображать оставшееся время до полного сведения в бою.
settings/aimingTimer/template/label: Шаблон для форматирования таймера сведения
settings/aimingTimer/template/tooltip: Доступные макросы:\n\n- %(aimingRemainingTime)s - оставшееся время до полного сведения\n- %(с:aimingStatus)s - цвет в зависимости от готовности (не готов, почти и готов)\n- %(aimingProgress)s - прогресс сведения в процентах
settings/aimingTimer/colors/aiming/label: Цвет: Не сведён
settings/aimingTimer/colors/aiming/tooltip: Цвет текста когда орудие в процессе сведения.
settings/aimingTimer/colors/almostDone/label: Цвет: Почти сведён
settings/aimingTimer/colors/almostDone/tooltip: Цвет текста когда орудие почти свелось.
settings/aimingTimer/colors/done/label: Цвет: Полностью сведён
settings/aimingTimer/colors/done/tooltip: Цвет текста когда орудие полностью свелось.
settings/aimingTimer/almostDoneThreshold/label: Пороговое значение "почти сведён"
settings/aimingTimer/almostDoneThreshold/tooltip: Определяет пороговое значение прогресса сведения (в процентах) когда таймер сведения получит статус "почти сведён".
settings/coolingDelayTimer/enabled/label: Включить таймер охлаждения орудия
settings/coolingDelayTimer/enabled/tooltip: Если включено, мод будет отображать время охлаждения орудия для танков, которые поддерживают механику двойного сведения (охлаждения орудия), такие как японские тяжёлые танки.
settings/hotkeys/toggle/label: Горячая клавиша: Переключатель мода
settings/hotkeys/toggle/tooltip: Выберите горячую клавишу для того, чтобы выключить либо включить мод в бою
battle/aimingTimer/done: Сведён!
battle/messages/enabled: Расширенная информация об состоянии техники игрока: ВКЛ
battle/messages/disabled: Расширенная информация об состоянии техники игрока: ВЫКЛ
battle/tip/text: Панель можно перетягивать мышкой.
battle/shellKind/normal: Обычный
battle/shellKind/premium: Премиум
battle/shellKind/stun: Станящий
