# CHANGELOG

### 1.4.7

- Added Fall Tanks (Neon Raid) gamemode to ignorelist

### 1.4.6

- Added Cosmic Event gamemode to ignorelist

### 1.4.5

- Minor update

### 1.4.4

- Added Grinch gamemode to ignorelist

### 1.4.3

- Localization implementation update
- Cleanup

### 1.4.2

- Added Races gamemode to ignorelist

### 1.4.1

- Added Cosmic Event battles to ignore list
- Removed obsolete info logs on mod start

### 1.4.0

- Migrate localization system from JSON to YAML

### 1.3.9

- Prevent replacing native WG assist counter when replace mode is enabled but modified assist counter is disabled
- Update project to Animate 2024

### 1.3.8

- Fixed missing decorator argument

### 1.3.7

- Sync up shared codebase and fixes
- Ignore cases when no additional efficiency fields are enabled in settings
- Minor refactor

### 1.3.6

- Minor fixes for AS3 method hooks

### 1.3.5

- Improved view loading logic

### 1.3.4

- Fixed missing Polish localization even on client with Polish language enabled

### 1.3.3

- Another minor flash rework

### 1.3.2

- Minor flash changes

### 1.3.1

- Added Polish translation - (c) Aslain
- Added missing enabled property to default config

### 1.3.0

- Added settings and localization support

### 1.2.1

- Fixes for AS3 `dispose` method
- Minor readme fixes

### 1.2.0

- Added ability to replace native asssist counters to configurable templates

### 1.1.0

- Added ability to separate native asssist counters

### 1.0.0

- Initial public release

