# scope: vehiclestate

settings/modDisplayName: Stan pojazdu
settings/healthInfo/template/label: Szablon formatu informacji o życiu
settings/healthInfo/template/tooltip: Dostępne makra:\n\n- %(health)s - aktualny poziom HP pojazdu gracza\n- %(c:health)s - kolor zależny od aktualnego poziomu HP pojazdu gracza\n- %(maxHealth)s - maksymalny poziom HP pojazdu gracza z uwzględnieniem wszystkich czynników (ulepszone hartowanie, modernizacja w terenie itp.)\n- %(healthPercentage)s - procent HP pojazdu obecnego gracza
settings/shellInfo/template/label: Szablon formatu informacji o pocisku
settings/shellInfo/template/tooltip: Dostępne makra:\n\n- %(shellDamage)s - uszkodzenia załadowanego pocisku\n- %(shellDamageDispersion)s - rozrzut załadowanego pocisku\n- %(shellPenetration)s - penetracja załadowanego pocisku\n- %(shellPenetrationDispersion)s - penetracja pocisku z rozrzutem +-25%\n- %(shellType)s - typ pocisku\n- %(shellKind)s - rodzaj pocisku (normalny, premium lub ogłuszający)\n- %(c:shellKind)s - kolor pocisku (normalny, premium lub ogłuszający)\n- %(shellSpeed)s - aktualna prędkość pocisku\n- %(shellCaliber)s - current shell caliber\n- %(gunElevation)s - aktualny kąt podniesienia działa pojazdu\n- %(gunDepression)s - aktualny kąt obniżenia działa pojazdu\n- %(gunPitchLimits)s - aktualny kąt podniesienia i obniżenia działa pojazdu.
settings/prettyNumberFormat/label: Włącz ładny format liczb
settings/prettyNumberFormat/tooltip: Jeśli włączone, otrzymasz sformatowany numer, np. 1 400 lub 1,400 zamiast 1400, w zależności od ustawień systemowych.
settings/align/label: Pozycja tekstu w panelu
settings/align/tooltip: Określa wyrównanie tekstu panelu.
settings/align/left/option: Lewa
settings/align/center/option: Środkowa
settings/align/right/option: Prawa
settings/aimingTimer/enabled/label: Włącz licznik celowania
settings/aimingTimer/enabled/tooltip: Po włączeniu modyfikacja będzie wyświetlać pozostały czas do całkowitego wycelowania podczas bitwy.
settings/aimingTimer/template/label: Szablon formatu licznika celowania.
settings/aimingTimer/template/tooltip: Dostępne makra:\n\n- %(aimingRemainingTime)s - pozostały czas do całkowitego wycelowania w sekundach\n- %(с:aimingStatus)s - kolor zależny od bieżącego statusu celowania (celowanie, prawie zakończone i zakończone)\n- %(aimingProgress)s - postęp celowania w procentach
settings/aimingTimer/colors/aiming/label: Kolor: Celowanie
settings/aimingTimer/colors/aiming/tooltip: Kolor tekstu, gdy trwa celowanie.
settings/aimingTimer/colors/almostDone/label: Kolor: Prawie ukończone celowanie
settings/aimingTimer/colors/almostDone/tooltip: Kolor tekstu, gdy proces celowania jest prawie zakończony.
settings/aimingTimer/colors/done/label: Kolor: Zakończone celowanie
settings/aimingTimer/colors/done/tooltip: Kolor tekstu, gdy proces celowania został zakończony.
settings/aimingTimer/almostDoneThreshold/label: Progi dla licznika celowania - prawie zakończony
settings/aimingTimer/almostDoneThreshold/tooltip: Określ procentowy próg postępu, przy którym minutnik celowania zostanie ustawiony na status - prawie zakończony.
settings/coolingDelayTimer/enabled/label: Włącz licznik opóźnienia chłodzenia działa
settings/coolingDelayTimer/enabled/tooltip: o włączeniu, mod będzie wyświetlać czas opóźnienia chłodzenia dla pojazdów (ciężkie japońskie czołgi), które wspierają podwójne celowanie lub mechanizm chłodzenia działa.
settings/hotkeys/toggle/label: Skrót dla: Przełącz tryb
settings/hotkeys/toggle/tooltip: Wybierz klawisz skrótu do przełączania tryby moda w bitwie
battle/aimingTimer/done: Gotowe!
battle/messages/enabled: Stan pojazdu: WŁĄCZONY
battle/messages/disabled: Stan pojazdu: WYŁĄCZONY
battle/tip/text: Przesuwaj panel za pomocą myszki.
battle/shellKind/normal: Normalny
battle/shellKind/premium: Premium
battle/shellKind/stun: Ogłuszający
