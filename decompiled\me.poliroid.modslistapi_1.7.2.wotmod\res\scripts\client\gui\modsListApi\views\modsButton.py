# uncompyle6 version 3.9.2
# Python bytecode version base 2.7 (62211)
# Decompiled from: Python 3.12.6 (tags/v3.12.6:a4a2d2b, Sep  6 2024, 20:11:23) [MSC v.1940 64 bit (AMD64)]
# Embedded file name: python\gui\modsListApi\views\modsButton.py
# Compiled at: 2025-09-01 06:56:13
"""
This module contains the view for the ModsList API button.
"""
from frameworks.wulf import ViewModel
from gui.impl.pub.view_component import ViewComponent
from gui.Scaleform.framework.entities.inject_component_adaptor import InjectComponentAdaptor
from openwg_gameface import ModDynAccessor, gf_mod_inject
from .._constants import MODS_LIST_BUTTON_VIEW
from ..controller import g_controller
from ..data import g_dataProvider
from ..events import g_eventsManager
from ..lang import l10n

class ModsButtonModel(ViewModel):
    """
    The view model for the ModsList API button.
    """

    def __init__(self, properties=3, commands=1):
        super(ModsButtonModel, self).__init__(properties=properties, commands=commands)

    def _initialize(self):
        """
        Initializes the view model.
        """
        super(ModsButtonModel, self)._initialize()
        self._addStringProperty('title', l10n('title'))
        self._addStringProperty('description', l10n('description'))
        self._addNumberProperty('alerts', g_dataProvider.alertsCount)
        self.onButtonClick = self._addCommand('onButtonClick')
        gf_mod_inject(self, MODS_LIST_BUTTON_VIEW, styles=[
         'coui://gui/gameface/mods/poliroid/ModsListButton/ModsListButton.css'], modules=[
         'coui://gui/gameface/mods/poliroid/ModsListButton/ModsListButton.js'])

    def setTitle(self, value):
        self._setString(0, value)

    def getTitle(self):
        return self._getString(0)

    def setDescription(self, value):
        self._setString(1, value)

    def getDescription(self):
        return self._getString(1)

    def setAlerts(self, value):
        self._setNumber(2, value)

    def getAlerts(self):
        return self._getNumber(2)


class ModsButtonView(ViewComponent[ModsButtonModel]):
    """
    The view for the ModsList API button.
    """
    buttonLayoutID = ModDynAccessor(MODS_LIST_BUTTON_VIEW)

    def __init__(self):
        super(ModsButtonView, self).__init__(layoutID=ModsButtonView.buttonLayoutID(), model=ModsButtonModel)

    @property
    def viewModel(self):
        """
        Gets the view model.
        """
        return super(ModsButtonView, self).getViewModel()

    def _finalize(self):
        """
        Disposes of the view.
        """
        g_eventsManager.onListUpdated -= self.__onListUpdated
        super(ModsButtonView, self)._finalize()

    def _onLoading(self, *args, **kwargs):
        """
        Initializes the view.
        """
        super(ModsButtonView, self)._onLoading()
        g_eventsManager.onListUpdated += self.__onListUpdated

    def __onListUpdated(self):
        """
        Handles the list updated event.
        """
        alertsCount = g_dataProvider.alertsCount
        self.viewModel.setAlerts(alertsCount)

    def _getEvents(self):
        """
        Gets the view events.
        """
        return (
         (
          self.viewModel.onButtonClick, self.__onButtonClick),)

    def __onButtonClick(self, context={}):
        """
        Handles the button click event.
        """
        g_controller.isInLobby = not context.get('standalone', False)
        g_eventsManager.showPopover()


class ModsButtonInjectComponent(InjectComponentAdaptor):
    """
    Injects adaptor for standalone button in the login window.
    """

    def _makeInjectView(self):
        """
        Creates the inject view.
        """
        return ModsButtonView()
