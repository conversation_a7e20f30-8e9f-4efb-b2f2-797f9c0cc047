# Import the necessary modules
import BigWorld
from gui.shared import events, EVENT_BUS_SCOPE, g_eventBus
from gui.shared.personality import ServicesLocator
from gui.app_loader.settings import APP_NAME_SPACE

# Define the mod class
class WelcomeMod:
    def __init__(self):
        print("WelcomeMod: Initializing...")
        # Use proper event-based initialization instead of simple callback
        g_eventBus.addListener(events.AppLifeCycleEvent.INITIALIZED, self._on_app_initialized, scope=EVENT_BUS_SCOPE.GLOBAL)

        # Also add a backup timer-based approach in case event doesn't fire
        BigWorld.callback(5.0, self._backup_show_message)

    def _on_app_initialized(self, event):
        """Handle the application initialization event"""
        print("WelcomeMod: App initialized event received, namespace:", getattr(event, 'ns', 'unknown'))

        # Check if this is the lobby app initialization
        if hasattr(event, 'ns') and event.ns == APP_NAME_SPACE.SF_LOBBY:
            print("WelcomeMod: Lobby app initialized, showing welcome message...")
            # Add a small delay to ensure everything is ready
            BigWorld.callback(2.0, self.show_welcome_message)
        else:
            print("WelcomeMod: Non-lobby app initialized, waiting for lobby...")

    def _backup_show_message(self):
        """Backup method to show message if event-based approach fails"""
        print("WelcomeMod: Backup timer triggered, checking app state...")

        # Check if lobby app is available
        try:
            app = ServicesLocator.appLoader.getApp(APP_NAME_SPACE.SF_LOBBY)
            if app:
                print("WelcomeMod: Lobby app found via backup method, showing message...")
                self.show_welcome_message()
            else:
                print("WelcomeMod: Lobby app not ready yet, trying again in 3 seconds...")
                BigWorld.callback(3.0, self._backup_show_message)
        except Exception as e:
            print("WelcomeMod: Error in backup method:", str(e))
            # Still try to show the message
            self.show_welcome_message()

    def show_welcome_message(self):
        """Display the welcome notification using multiple fallback approaches"""
        print("WelcomeMod: Attempting to show welcome message...")

        # Method 1: Try modern SystemMessages approach
        if self._try_system_messages():
            return

        # Method 2: Try alternative SystemMessages import
        if self._try_alternative_system_messages():
            return

        # Method 3: Try messenger system
        if self._try_messenger_system():
            return

        # Method 4: Try chat system
        if self._try_chat_system():
            return

        # Final fallback - console only
        print("WelcomeMod: All notification methods failed, using console fallback")
        print("=" * 50)
        print("COME STAI? - Welcome to World of Tanks!")
        print("=" * 50)

    def _try_system_messages(self):
        """Try the standard SystemMessages approach"""
        try:
            from gui.SystemMessages import pushMessage, SM_TYPE
            pushMessage('COME STAI?', SM_TYPE.Information)
            print("WelcomeMod: Notification sent via SystemMessages!")
            return True
        except ImportError as e:
            print("WelcomeMod: SystemMessages import failed:", str(e))
            return False
        except Exception as e:
            print("WelcomeMod: SystemMessages error:", str(e))
            return False

    def _try_alternative_system_messages(self):
        """Try alternative SystemMessages import paths"""
        try:
            # Try different import path
            from gui.shared.system_messages import pushMessage, SM_TYPE
            pushMessage('COME STAI?', SM_TYPE.Information)
            print("WelcomeMod: Notification sent via alternative SystemMessages!")
            return True
        except ImportError:
            try:
                # Try another path
                import gui.SystemMessages as SystemMessages
                SystemMessages.pushMessage('COME STAI?', SystemMessages.SM_TYPE.Information)
                print("WelcomeMod: Notification sent via imported SystemMessages!")
                return True
            except Exception as e:
                print("WelcomeMod: Alternative SystemMessages failed:", str(e))
                return False
        except Exception as e:
            print("WelcomeMod: Alternative SystemMessages error:", str(e))
            return False

    def _try_messenger_system(self):
        """Try the messenger system approach"""
        try:
            from messenger import MessagesController
            from messenger.m_constants import SCH_CLIENT_MSG_TYPE

            if hasattr(MessagesController, 'g_instance') and MessagesController.g_instance:
                MessagesController.g_instance.onReceiveMessage(
                    SCH_CLIENT_MSG_TYPE.SYS_MSG_TYPE.information,
                    'COME STAI?',
                    {}
                )
                print("WelcomeMod: Notification sent via MessagesController!")
                return True
            else:
                print("WelcomeMod: MessagesController.g_instance not available")
                return False
        except ImportError as e:
            print("WelcomeMod: MessagesController import failed:", str(e))
            return False
        except Exception as e:
            print("WelcomeMod: MessagesController error:", str(e))
            return False

    def _try_chat_system(self):
        """Try using the chat system for notification"""
        try:
            # Try to use BigWorld player chat
            player = BigWorld.player()
            if player and hasattr(player, 'showMessage'):
                player.showMessage('COME STAI?')
                print("WelcomeMod: Notification sent via player.showMessage!")
                return True
            elif player and hasattr(player, 'base') and hasattr(player.base, 'showMessage'):
                player.base.showMessage('COME STAI?')
                print("WelcomeMod: Notification sent via player.base.showMessage!")
                return True
            else:
                print("WelcomeMod: Player chat methods not available")
                return False
        except Exception as e:
            print("WelcomeMod: Chat system error:", str(e))
            return False

    def cleanup(self):
        """Clean up event listeners"""
        try:
            g_eventBus.removeListener(events.AppLifeCycleEvent.INITIALIZED, self._on_app_initialized, scope=EVENT_BUS_SCOPE.GLOBAL)
            print("WelcomeMod: Event listeners cleaned up")
        except Exception as e:
            print("WelcomeMod: Cleanup error:", str(e))

# Instantiate the mod
print("WelcomeMod: Creating mod instance...")
g_welcomeMod = WelcomeMod()