# scope: advancedpersonalefficiency

settings/modDisplayName: Zaawansowana Wydaj<PERSON>ść Osobista
settings/assist/enabled/label: <PERSON><PERSON><PERSON><PERSON> zmodyfikowany licznik asyst
settings/assist/enabled/tooltip: Włącza możliwość rozszerzenia licznika asyst za pomocą „Trybu licznika asyst”.
settings/assist/mode/label: Tryb licznika asyst
settings/assist/mode/tooltip: W<PERSON><PERSON>rz zachowanie licznika asyst.\n\n* Radio - dodaje dodatkowy licznik asyst radiowych\n* Gąsienica - dodaje dodatkowy licznik asyst związanych z gąsienicą\n* Obie - dodaje dodatkowe liczniki asyst radiowych i związanych z gąsienicą\n* Zastąp - zastępuje wartość oryginalnego licznika asyst sformatowaną wartością asyst z „Formatem licznika asyst”
settings/assist/mode/radio/option: Radio
settings/assist/mode/track/option: Gąsienica
settings/assist/mode/both/option: Obie
settings/assist/mode/replace/option: Zastąp
settings/assist/format/label: Format oryginalnego licznika asyst
settings/assist/format/tooltip: Dostosuj wartość oryginalnego licznika asyst za pomocą makr.\n\nDostępne makra: \n* {{totalAssist}} - suma wartości asyst radiowych i związanych z gąsienicą\n* {{radioAssist}} - wartość asyst radiowych\n* {{trackAssist}} - wartość asyst związanych z gąsienicą\n\nDomyślna wartość: {{totalAssist}} ({{radioAssist}} / {{trackAssist}})
settings/spotted/enabled/label: Włącz licznik wyspotowanych pojazdów
settings/spotted/enabled/tooltip: Dodaje dodatkowy licznik wyspotowanych pojazdów do panelu dziennika uszkodzeń.
settings/crits/enabled/label: Włącz licznik uszkodzeń krytycznych
settings/crits/enabled/tooltip: Dodaje dodatkowy licznik uszkodzeń krytycznych urządzeń do panelu dziennika uszkodzeń.
