##### World of Tanks - Mods

## .. GUIFlash ..

Copyright: (c) 2017-2019 GambitER, since 2020 CH4MPi

This project is maintained by CH4MPi

## English
Allows you to dynamically create components for displaying text and graphic information in the Flash interface of the game World of Tanks. Components are created and managed using Python scripts (client modifications).

Learn more @ [**Wiki**](https://github.com/CH4MPi/GUIFlash/wiki).

## Russian
Позволяет динамически создавать компоненты для отображения текстовой и графической информации в Flash интерфейсе игры World of Tanks. Создание и управление компонентами производится через Python скрипты (модификации клиента)._

Подробнее в [**Wiki**](https://github.com/GambitER/GUIFlash/wiki).


![https://img.shields.io/badge/Status-WIP-orange](https://img.shields.io/badge/Status-WIP-orange) ![https://img.shields.io/badge/Realese-Yes-green](https://img.shields.io/badge/Realese-Yes-green) ![https://img.shields.io/badge/Python-2.7-blue](https://img.shields.io/badge/Python-2.7-blue)
