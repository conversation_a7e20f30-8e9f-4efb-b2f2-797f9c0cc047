# uncompyle6 version 3.9.2
# Python bytecode version base 2.7 (62211)
# Decompiled from: Python 3.12.6 (tags/v3.12.6:a4a2d2b, Sep  6 2024, 20:11:23) [MSC v.1940 64 bit (AMD64)]
# Embedded file name: python\gui\modsListApi\views\modsPopover.py
# Compiled at: 2025-08-31 19:33:10
"""
This module contains the popover view for the ModsList API.
"""
from gui.Scaleform.framework.entities.abstract.AbstractPopOverView import AbstractPopOverView
from ..data import g_dataProvider
from ..events import g_eventsManager

class ModsListPopoverViewMeta(AbstractPopOverView):
    """
    Meta class for the ModsListPopoverView.
    """

    def getModsList(self):
        """
        Gets the list of mods from the client.
        """
        self._printOverrideError('getModsList')

    def invokeMod(self, modification_id):
        """
        Invokes a mod by its ID.
        """
        self._printOverrideError('invokeMod')

    def as_setModsDataS(self, data):
        """
        Sets the mods data for the popover.
        :param data: Represented by ModsListModsVO (AS)
        """
        if self._isDAAPIInited():
            return self.flashObject.as_setModsData(data)

    def as_setStaticDataS(self, data):
        """
        Sets the static data for the popover.
        :param data: Represented by ModsListStaticDataVO (AS)
        """
        if self._isDAAPIInited():
            return self.flashObject.as_setStaticData(data)


class ModsListPopoverView(ModsListPopoverViewMeta):
    """
    The popover view for the ModsList API.
    """

    def _populate(self):
        """
        Initializes the view.
        """
        super(ModsListPopoverView, self)._populate()
        g_eventsManager.onListUpdated += self.__collectModsData
        self.as_setStaticDataS(g_dataProvider.staticData)
        self.__collectModsData()

    def _dispose(self):
        """
        Disposes of the view.
        """
        g_eventsManager.onListUpdated -= self.__collectModsData
        super(ModsListPopoverView, self)._dispose()

    def __collectModsData(self):
        """
        Collects and sets the mods data.
        """
        self.as_setModsDataS(g_dataProvider.modsData)

    def getModsList(self):
        """
        Gets the list of mods from the client.
        """
        self.__collectModsData()

    def invokeModification(self, modification_id):
        """
        Invokes a mod by its ID.
        """
        g_eventsManager.invokeModification(modification_id)
