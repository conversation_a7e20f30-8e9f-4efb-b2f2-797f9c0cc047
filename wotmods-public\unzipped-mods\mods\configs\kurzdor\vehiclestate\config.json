{
  "enabled": true,
  "visible": true,
  "align": "left",
  "offsets": {
    "arcade": {
      "x": 172,
      "y": -83
    },
    "sniper": {
      "x": 278,
      "y": 0
    },
    "strategic": {
      "x": 350,
      "y": 0
    }
  },
  "templates": {
    // Aiming info template
    // Шаблон информации про сведение танка
    // Available macroses:
    // Доступные макросы:
    // %(aimingRemainingTime)s - remaining aiming time | оставшееся время до полного сведения
    // %(c:aimingStatus)s - color depending on status (not ready, almost ready, ready) | цвет в зависимости от готовности (не готов, почти и готов)
    // %(aimingProgress)s - aiming progress in percents | прогресс сведения в процентах
    "aimingTimer": "<font face='$FieldFont' size='16' color='%(c:aimingStatus)s'>%(aimingRemainingTime)s - %(aimingProgress)s</font>",
    // Health info template
    // Шаблон информации про состояние прочности танка
    // Available macroses:
    // Доступные макросы:
    // %(health)s - current player's vehicle HP level | текущий уровень ХП техники игрока
    // %(c:health)s - color depending of current player's vehicle HP level | цвет в зависимости от текущего уровня ХП игрока
    // %(maxHealth)s - max player's vehicle HP level including all factors (improved hardening, field modernization, etc.) | максимальный уровень ХП с учётом всех факторов (улучшенная закалка, полевая модернизация и прочее) техники игрока
    // %(healthPercentage)s - current player's vehicle HP percentage | текущее значение прочности техники игрока в процентах
    "healthInfo": "<font face='$FieldFont' size='16' color='%(c:health)s'>%(health)s</font>",
    // Shell info template
    // Шаблон информации про текущий снаряд танка
    // Available macroses:
    // Доступные макросы:
    // %(shellDamage)s - current loaded shell damage | урон заряженного снаряда
    // %(shellDamageDispersion)s - current loaded shell damage dispersion | разброс урона заряженного снаряда (с учётом +-25%)
    // %(shellPenetration)s - current loaded shell penetration | пробитие заряженного снаряда
    // %(shellPenetrationDispersion)s - current shell penetration with +-25% dispersion | разброс пробития заряженного снаряда (с учётом +-25%)
    // %(shellType)s - current shell type | текущий тип заряженного снаряда
    // %(shellKind)s - current shell kind (normal, premium or stun) | текущий тип заряженного снаряда (обычный, премиум или станящий)
    // %(c:shellKind)s - current shell type color (normal, premium or stun) | цвет текущего типа заряженного снаряда (обычный, премиум или станящий)
    // %(shellSpeed)s - current shell speed | скорость полёта заряженного снаряда
    // %(shellCaliber)s - current shell caliber | кабибр заряженного снаряда
    // %(gunElevation)s - gun elevation | максимальный верхний угол вертикальной наводки танка
    // %(gunDepression)s - gun depression | максимальный нижний угол вертикальной наводки танка
    // %(gunPitchLimits)s - gun elevation and depression | углы вертикальной наводки танка
    "shellInfo": "<font face='$FieldFont' size='16'>~%(shellPenetration)s (%(shellPenetrationDispersion)s) <font color='%(c:shellKind)s'>(%(shellType)s)</font></font>"
  },
  // Enable aiming circle timer
  // Включить таймер сведения
  "aimingTimer": {
    "enabled": true,
    // Threshold for "Almost done" status
    // Пороговое значение для статуса "Почти готов"
    // Minimum value: 50, maximum: 99
    // Минимальное значение: 50, максимальное: 99
    "almostDoneThreshold": 75
  },
  // Enable cooling delay timer for Japanese HTs
  // Включить таймер охладжения орудия для японских тяжей
  "coolingDelayTimer": {
    "enabled": true
  },
  "shadow": {
    "distance": 1,
    "angle": 90,
    "color": "#000000",
    "alpha": 70,
    "blur": 2,
    "strength": 4
  },
  "hotkeys": {
    "toggle": [[29, 157], 66]
  },
  // Enable pretty number format, i.e. instead 1400 you will get number like 1 400 or 1,400 depending on system settings
  // Включить красивый формат цифр, например вместо 1400 вы получите число вида 1 400 либо 1,400 в зависимости от системных настроек
  "prettyNumberFormat": false,
  "version": 3
}
